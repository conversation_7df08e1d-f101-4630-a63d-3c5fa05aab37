name: Docker Image CI

on:
  push:
    branches: [ "main" ]

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    environment: production
    permissions:
      contents: read
      packages: write # Required for publishing to GHCR, adjust if using Docker Hub

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }} # Replace with your Docker Hub username secret
          password: ${{ secrets.DOCKERHUB_TOKEN }}   # Replace with your Docker Hub token secret

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/central-api:latest
          build-args: |
            NODE_ENV=production