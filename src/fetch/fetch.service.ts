import { Injectable, Logger } from '@nestjs/common';
import { spawn } from 'child_process';
import * as path from 'path';
import { Observable, from, throwError, Subscriber } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
    AxiosRequestConfig,
    AxiosResponse,
    AxiosError,
    AxiosHeaders,
    InternalAxiosRequestConfig,
    AxiosHeaderValue,
    AxiosProxyConfig
} from 'axios';

export interface FetchDefaultOptions {
  proxyString?: string;
  timeout?: number;
}

function normalizeHeadersForAxiosHeadersStatic(
    headers: AxiosRequestConfig['headers']
): Record<string, AxiosHeaderValue> | undefined {
    try {
        // If headers is undefined, return an empty object
        if (typeof headers === 'undefined') {
            return {};
        }

        // If headers is already an AxiosHeaders instance, convert to plain object
        if (headers instanceof AxiosHeaders) {
            try {
                return headers.toJSON();
            } catch (error) {
                console.error('Error converting AxiosHeaders to JSON:', error);
                return {};
            }
        }

        // If headers is a string, return an empty object (can't parse string headers)
        if (typeof headers === 'string') {
            return {};
        }

        // Otherwise, assume it's an object and normalize it
        const newHeaders: Record<string, AxiosHeaderValue> = {};

        // Safely iterate through headers
        if (headers && typeof headers === 'object') {
            for (const key in headers) {
                if (Object.prototype.hasOwnProperty.call(headers, key)) {
                    const value = headers[key as keyof typeof headers];
                    if (value !== undefined && value !== null) {
                        if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || Array.isArray(value)) {
                            newHeaders[key] = value as AxiosHeaderValue;
                        } else {
                            newHeaders[key] = String(value);
                        }
                    }
                }
            }
        }

        return newHeaders;
    } catch (error) {
        console.error('Error in normalizeHeadersForAxiosHeadersStatic:', error);
        return {};
    }
}

function buildAxiosResponse<T>(
  data: T,
  status: number,
  statusText: string,
  originalConfig: AxiosRequestConfig,
  curlCommand: string,
  responseHeaders?: Record<string, string>, // Note: We don't get response headers from the script
): AxiosResponse<T> {
  // Create a safe version of the headers
  let safeHeaders;
  try {
    const normalizedHeaders = normalizeHeadersForAxiosHeadersStatic(originalConfig.headers);
    if (normalizedHeaders instanceof AxiosHeaders) {
      safeHeaders = normalizedHeaders;
    } else if (typeof normalizedHeaders === 'object') {
      safeHeaders = {};
      Object.entries(normalizedHeaders).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          safeHeaders[key] = value;
        }
      });
    } else {
      safeHeaders = {};
    }
  } catch (error) {
    console.error('Error processing headers in buildAxiosResponse:', error);
    safeHeaders = {};
  }

  const internalConfig: InternalAxiosRequestConfig = {
    ...originalConfig,
    headers: safeHeaders,
  };
  return {
    data,
    status,
    statusText,
    headers: responseHeaders || {}, // Will be empty as we don't parse them
    config: internalConfig,
    request: { command: curlCommand },
  };
}

function buildAxiosError(
  message: string,
  originalConfig: AxiosRequestConfig,
  code?: string,
  curlCommand?: string,
  response?: AxiosResponse,
): AxiosError {
  // Create a safe version of the headers
  let safeHeaders;
  try {
    const normalizedHeaders = normalizeHeadersForAxiosHeadersStatic(originalConfig.headers);
    if (normalizedHeaders instanceof AxiosHeaders) {
      safeHeaders = normalizedHeaders;
    } else if (typeof normalizedHeaders === 'object') {
      safeHeaders = {};
      Object.entries(normalizedHeaders).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          safeHeaders[key] = value;
        }
      });
    } else {
      safeHeaders = {};
    }
  } catch (error) {
    console.error('Error processing headers in buildAxiosError:', error);
    safeHeaders = {};
  }

  const internalConfig: InternalAxiosRequestConfig = {
    ...originalConfig,
    headers: safeHeaders,
  };
  const error: AxiosError = new Error(message) as AxiosError;
  error.config = internalConfig;
  if (code) {
    error.code = code;
  }
  if (curlCommand) {
    error.request = { command: curlCommand };
  }
  if (response) {
    error.response = response;
  }
  error.isAxiosError = true;
  return error;
}


@Injectable()
export class FetchService {
  private readonly logger = new Logger(FetchService.name);
  private readonly curlCommand = 'curl_chrome116'; // Changed from curlBinaryPath

  constructor(private readonly defaultOptions?: FetchDefaultOptions) {
    this.logger.log(`FetchService initialized. curl command set to: ${this.curlCommand}`);
    this.logger.log(`Node.js process.env.PATH: ${process.env.PATH}`); // Log PATH for debugging

    if (this.defaultOptions?.proxyString) {
      this.logger.log(`FetchService initialized with default proxy: ${this.defaultOptions.proxyString}`);
    }
    if (this.defaultOptions?.timeout) {
      this.logger.log(`FetchService initialized with default timeout: ${this.defaultOptions.timeout}ms`);
    }
  }

  private convertAxiosProxyToCurlOpt(proxyConfig: AxiosProxyConfig): string | undefined {
    if (proxyConfig.host && proxyConfig.port) {
        let protocol = proxyConfig.protocol ? `${proxyConfig.protocol}://` : 'http://';
        let authString = '';
        if (proxyConfig.auth) {
            authString = `${proxyConfig.auth.username}:${proxyConfig.auth.password}@`;
        }
        return `${protocol}${authString}${proxyConfig.host}:${proxyConfig.port}`;
    }
    return undefined;
  }

  private executeCurlViaSpawn<T = any>(
    effectiveUrl: string,
    config: AxiosRequestConfig,
  ): Promise<AxiosResponse<T>> {
    return new Promise<AxiosResponse<T>>((resolve, reject) => {
      const scriptArgs: string[] = [];

      const method = (config.method || 'GET').toUpperCase();
      if (method !== 'GET') {
          scriptArgs.push('-X', method);
      }

      let finalProxyString: string | undefined = undefined;
      if (config.proxy === false) {
          this.logger.debug('Proxy explicitly disabled for this request.');
      } else if (config.proxy) {
          finalProxyString = this.convertAxiosProxyToCurlOpt(config.proxy);
          if (finalProxyString) {
              this.logger.debug(`Using per-request proxy: ${finalProxyString}`);
          } else {
              this.logger.warn('Per-request proxy config provided but invalid, ignoring.', config.proxy);
          }
      } else if (this.defaultOptions?.proxyString) {
          finalProxyString = this.defaultOptions.proxyString;
          this.logger.debug(`Using default proxy: ${finalProxyString}`);
      }

      if (finalProxyString) {
          scriptArgs.push('-x', finalProxyString);
      }

      if (config.headers) {
        try {
          const normalizedHeaders = normalizeHeadersForAxiosHeadersStatic(config.headers);

          // Handle headers safely without relying on forEach
          if (normalizedHeaders) {
            if (normalizedHeaders instanceof AxiosHeaders) {
              // Use AxiosHeaders methods if available
              Object.entries(normalizedHeaders.toJSON()).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                  scriptArgs.push('-H', `${key}: ${value}`);
                }
              });
            } else if (typeof normalizedHeaders === 'object') {
              // Handle plain object headers
              Object.entries(normalizedHeaders).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                  scriptArgs.push('-H', `${key}: ${value}`);
                }
              });
            }
          }
        } catch (error) {
          this.logger.error(`Error processing headers: ${error.message}`, error);
          // Continue without headers rather than failing the request completely
        }
      }

      if (config.data) {
        if (typeof config.data === 'string') {
          scriptArgs.push('-d', config.data);
        } else if (typeof config.data === 'object') {
          try {
              const normalizedHeaders = normalizeHeadersForAxiosHeadersStatic(config.headers);
              let contentTypeHeader = '';

              try {
                if (normalizedHeaders instanceof AxiosHeaders) {
                  contentTypeHeader = normalizedHeaders.getContentType()?.toString() || '';
                } else if (typeof normalizedHeaders === 'object') {
                  // Try to find content-type in the headers object
                  const contentTypeKey = Object.keys(normalizedHeaders)
                    .find(key => key.toLowerCase() === 'content-type');
                  if (contentTypeKey) {
                    contentTypeHeader = String(normalizedHeaders[contentTypeKey]);
                  }
                }
              } catch (error) {
                this.logger.warn(`Error getting content type: ${error.message}`);
              }
              if (contentTypeHeader.includes('application/json')) {
                   scriptArgs.push('-d', JSON.stringify(config.data));
              } else if (contentTypeHeader.includes('application/x-www-form-urlencoded')) {
                  scriptArgs.push('-d', new URLSearchParams(config.data as Record<string, string>).toString());
              }
              else {
                   this.logger.warn('Non-string data provided without explicit JSON/form content type, sending as is. This might not work as expected.');
                   scriptArgs.push('-d', String(config.data));
              }
          } catch (e) {
              this.logger.error('Failed to stringify data object', e);
              reject(buildAxiosError('Failed to process data object', config, 'ERR_DATA_PROCESSING'));
              return;
          }
        }
      }

      scriptArgs.push('-sSL');
      scriptArgs.push(effectiveUrl);

      const commandToExecute = this.curlCommand; // Use the direct command
      const commandStringForLog = `${commandToExecute} ${scriptArgs.join(' ')}`;
      this.logger.debug(`Executing command: ${commandStringForLog}`);

      const finalTimeout = config.timeout ?? this.defaultOptions?.timeout ?? 30000;
      this.logger.debug(`Using timeout: ${finalTimeout}ms`);

      let stdoutData = '';
      let stderrData = '';

      const shellToUse = '/bin/sh';
      this.logger.log(`Attempting to use shell: ${shellToUse}`);

      const child = spawn(commandToExecute, scriptArgs, {
        timeout: finalTimeout,
        // cwd: undefined, // Let it use the default CWD of the Node.js process
        windowsHide: true,
      });

      child.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      child.on('error', (error) => {
        this.logger.error(`spawn error for command "${commandToExecute}" with shell ${shellToUse}: ${error.message}`, error.stack);
        const errorCode = (error as NodeJS.ErrnoException).code || 'ERR_SPAWN_ERROR';
        reject(buildAxiosError(`Script execution spawn error: ${error.message}`, config, errorCode, commandStringForLog));
      });

      child.on('close', (code) => {
        if (stderrData) {
          this.logger.warn(`curl_chrome116 script stderr: ${stderrData.trim()}`);
        }

        if (code !== 0) {
          const message = `Script exited with code ${code}.`;
          this.logger.error(`${message} URL: ${effectiveUrl}`);
          reject(buildAxiosError(`${message}\nstderr: ${stderrData.trim()}`, config, `ERR_SCRIPT_EXIT_${code}`, commandStringForLog));
          return;
        }

        // If script exits with 0 and we have stdout, assume HTTP 200 OK
        const statusCode = 200;
        const statusText = 'OK';
        const responseBody = stdoutData.trim(); // stdoutData is the full body

        this.logger.log(`Script exited successfully (code 0). Assuming HTTP 200. Body length: ${responseBody.length}`);

        let responseData: any = responseBody;
        let acceptHeader = '';

        try {
          const normalizedAcceptHeaders = normalizeHeadersForAxiosHeadersStatic(config.headers);

          // Try to find accept header in different ways
          if (normalizedAcceptHeaders) {
            // Look for 'accept' in the headers object
            const acceptKey = Object.keys(normalizedAcceptHeaders)
              .find(key => key.toLowerCase() === 'accept');

            if (acceptKey) {
              acceptHeader = String(normalizedAcceptHeaders[acceptKey]);
            }
          }
        } catch (error) {
          this.logger.warn(`Error getting accept header: ${error.message}`);
        }
        if (acceptHeader.includes('application/json') && responseBody) { // Check if responseBody is not empty
            try {
                responseData = JSON.parse(responseBody);
            } catch (e) {
                this.logger.warn('Accept header suggests JSON, but failed to parse response body.', e);
            }
        }

        const response = buildAxiosResponse(
          responseData as T,
          statusCode,
          statusText,
          config,
          commandStringForLog,
          // Response headers are not available from the script output directly
        );

        resolve(response);
      });
    });
  }


  request<T = any>(config: AxiosRequestConfig): Observable<AxiosResponse<T>> {
    const url = config.url;
    if (!url) {
        return throwError(() => buildAxiosError('URL is required in AxiosRequestConfig', config, 'ERR_INVALID_URL'));
    }
    return from(this.executeCurlViaSpawn<T>(url, config)).pipe(
      catchError(error => throwError(() => {
        if (error.isAxiosError) return error;
        return buildAxiosError(error.message, config, error.code, `Error before script execution for ${url}`);
      }))
    );
  }

  get<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  delete<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  head<T = any, D = any>(url: string, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'HEAD', url });
  }

  post<T = any, D = any>(url:string, data?: D, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  put<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  patch<T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Observable<AxiosResponse<T, D>> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }
}
