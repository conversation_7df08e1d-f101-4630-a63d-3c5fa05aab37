import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config'; // Import ConfigModule and ConfigService
import { FetchService, FetchDefaultOptions } from './fetch.service.js';

@Module({
  imports: [ConfigModule], // Import ConfigModule to use ConfigService
  providers: [
    {
      provide: FetchService,
      useFactory: (configService: ConfigService): FetchService => {
        const defaultOptions: FetchDefaultOptions = {};

        const proxyUrl = configService.get<string>('HTTP_PROXY');
        if (proxyUrl) {
          defaultOptions.proxyString = proxyUrl;
          // Log that proxy is being set from environment (CurlChrome116Tool will also log)
          // console.log(`FetchModule: Configuring CurlChrome116Tool with default proxy from HTTP_PROXY: ${proxyUrl}`);
        } else {
          // console.log('FetchModule: HTTP_PROXY not set, CurlChrome116Tool will not use a default proxy unless specified in request.');
        }

        // Example: Set a default timeout from environment or a fixed value
        const defaultTimeout = configService.get<number>('CURL_TOOL_DEFAULT_TIMEOUT');
        if (defaultTimeout && !isNaN(defaultTimeout)) {
            defaultOptions.timeout = defaultTimeout;
            // console.log(`FetchModule: Configuring CurlChrome116Tool with default timeout: ${defaultTimeout}ms`);
        } else {
            defaultOptions.timeout = 15000; // Default to 15 seconds if not specified or invalid
            // console.log(`FetchModule: Configuring CurlChrome116Tool with hardcoded default timeout: ${defaultOptions.timeout}ms`);
        }

        return new FetchService(defaultOptions);
      },
      inject: [ConfigService], // Inject ConfigService into the factory
    },
  ],
  exports: [FetchService],
})
export class FetchModule {}
