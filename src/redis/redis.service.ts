import { Injectable, Logger } from '@nestjs/common';
import { RedisService as NestJSRedisService } from '@liaoliaots/nestjs-redis';
import { Redis as IoRedis } from 'ioredis';

@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisService.name);
  private readonly redis: IoRedis;

  constructor(private readonly nestjsRedisService: NestJSRedisService) {
    this.redis = this.nestjsRedisService.getOrThrow();
    this.logger.log('RedisService initialized with nestjs-redis');
  }

  async get(key: string): Promise<string | null> {
    this.logger.log(`Attempting to get value for key: '${key}'`);
    const value = await this.redis.get(key);
    this.logger.log(`Value from redis.get for key '${key}', length: ${value?.length}`);
    return value;
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    this.logger.log(`Setting key='${key}', value length='${value?.length}', ttl=${ttl}`);
    try {
      if (ttl !== undefined) {
        await this.redis.set(key, value, 'EX', ttl);
      } else {
        await this.redis.set(key, value);
      }
      this.logger.log(`Successfully set key='${key}'`);
    } catch (error) {
      this.logger.error(`Error setting key='${key}':`, error);
      throw error;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    this.logger.log(`Attempting to get keys for pattern: '${pattern}'`);
    try {
      const keys = await this.redis.keys(pattern);
      this.logger.log(`Keys found for pattern '${pattern}': ${keys.length}`);
      return keys;
    } catch (error) {
      this.logger.error(`Error getting keys for pattern '${pattern}':`, error);
      throw error;
    }
  }

  async del(keys: string | string[]): Promise<number> {
    const keysToDelete = Array.isArray(keys) ? keys : [keys];
    if (keysToDelete.length === 0) {
      this.logger.log('No keys provided to delete.');
      return 0;
    }
    this.logger.log(`Attempting to delete keys: '${keysToDelete.join(', ')}'`);
    try {
      const count = await this.redis.del(...keysToDelete);
      this.logger.log(`Successfully deleted ${count} key(s).`);
      return count;
    } catch (error) {
      this.logger.error(`Error deleting keys '${keysToDelete.join(', ')}':`, error);
      throw error;
    }
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    this.logger.log(`Attempting to hset for key: '${key}', field: '${field}'`);
    try {
      const result = await this.redis.hset(key, field, value);
      this.logger.log(`Successfully hset for key '${key}', field '${field}'`);
      return result;
    } catch (error) {
      this.logger.error(`Error hset for key '${key}', field '${field}':`, error);
      throw error;
    }
  }

  async hget(key: string, field: string): Promise<string | null> {
    this.logger.log(`Attempting to hget for key: '${key}', field: '${field}'`);
    const value = await this.redis.hget(key, field);
    this.logger.log(`Value from redis.hget for key '${key}', field '${field}', length: ${value?.length}`);
    return value;
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    this.logger.log(`Attempting to hgetall for key: '${key}'`);
    try {
      const values = await this.redis.hgetall(key);
      this.logger.log(`Successfully hgetall for key '${key}'. Found ${Object.keys(values).length} fields.`);
      return values;
    } catch (error) {
      this.logger.error(`Error hgetall for key '${key}':`, error);
      throw error;
    }
  }

  async hdel(key: string, field: string | string[]): Promise<number> {
    const fieldsToDelete = Array.isArray(field) ? field : [field];
    if (fieldsToDelete.length === 0) {
      this.logger.log('No fields provided to hdel.');
      return 0;
    }
    this.logger.log(`Attempting to hdel for key: '${key}', fields: '${fieldsToDelete.join(', ')}'`);
    try {
      const count = await this.redis.hdel(key, ...fieldsToDelete);
      this.logger.log(`Successfully hdel ${count} field(s) for key '${key}'.`);
      return count;
    } catch (error) {
      this.logger.error(`Error hdel for key '${key}', fields '${fieldsToDelete.join(', ')}':`, error);
      throw error;
    }
  }

  async lpush(key: string, values: string | string[]): Promise<number> {
    const valuesToPush = Array.isArray(values) ? values : [values];
    if (valuesToPush.length === 0) {
      this.logger.log(`No values provided to lpush for key: '${key}'.`);
      return 0;
    }
    this.logger.log(`Attempting to lpush for key: '${key}', values: '${valuesToPush.join(', ')}'`);
    try {
      const count = await this.redis.lpush(key, ...valuesToPush);
      this.logger.log(`Successfully lpush ${count} value(s) for key '${key}'.`);
      return count;
    } catch (error) {
      this.logger.error(`Error lpush for key '${key}', values '${valuesToPush.join(', ')}':`, error);
      throw error;
    }
  }

  async rpush(key: string, values: string | string[]): Promise<number> {
    const valuesToPush = Array.isArray(values) ? values : [values];
    if (valuesToPush.length === 0) {
      this.logger.log(`No values provided to rpush for key: '${key}'.`);
      return 0;
    }
    this.logger.log(`Attempting to rpush for key: '${key}', values: '${valuesToPush.join(', ')}'`);
    try {
      const count = await this.redis.rpush(key, ...valuesToPush);
      this.logger.log(`Successfully rpush ${count} value(s) for key '${key}'.`);
      return count;
    } catch (error) {
      this.logger.error(`Error rpush for key '${key}', values '${valuesToPush.join(', ')}':`, error);
      throw error;
    }
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    this.logger.log(`Attempting to lrange for key: '${key}', start: ${start}, stop: ${stop}`);
    try {
      const values = await this.redis.lrange(key, start, stop);
      this.logger.log(`Successfully lrange for key '${key}'. Found ${values.length} values.`);
      return values;
    } catch (error) {
      this.logger.error(`Error lrange for key '${key}', start: ${start}, stop: ${stop}:`, error);
      throw error;
    }
  }

  async lrem(key: string, count: number, value: string): Promise<number> {
    this.logger.log(`Attempting to lrem for key: '${key}', count: ${count}, value: '${value}'`);
    try {
      const removedCount = await this.redis.lrem(key, count, value);
      this.logger.log(`Successfully lrem ${removedCount} value(s) for key '${key}'.`);
      return removedCount;
    } catch (error) {
      this.logger.error(`Error lrem for key '${key}', count: ${count}, value: '${value}':`, error);
      throw error;
    }
  }

  async llen(key: string): Promise<number> {
    this.logger.log(`Attempting to llen for key: '${key}'`);
    try {
      const length = await this.redis.llen(key);
      this.logger.log(`Successfully llen for key '${key}'. Length: ${length}`);
      return length;
    } catch (error) {
      this.logger.error(`Error llen for key '${key}':`, error);
      throw error;
    }
  }

  async mget(...keys: string[]): Promise<(string | null)[]> {
    this.logger.log(`Attempting to mget keys: '${keys.join(', ')}'`);
    try {
      const values = await this.redis.mget(...keys);
      this.logger.log(`Successfully mget for keys '${keys.join(', ')}'.`);
      return values;
    } catch (error) {
      this.logger.error(`Error mget for keys '${keys.join(', ')}':`, error);
      throw error;
    }
  }

  async scan(
    cursor: string,
    match: string,
    count: number,
  ): Promise<[string, string[]]> {
    this.logger.log(
      `Attempting to scan with cursor: ${cursor}, match: ${match}, count: ${count}`,
    );
    try {
      const result = await this.redis.scan(
        cursor,
        'MATCH',
        match,
        'COUNT',
        count,
      );
      this.logger.log(`Successfully scan.`);
      return result;
    } catch (error) {
      this.logger.error(
        `Error scan with cursor: ${cursor}, match: ${match}, count: ${count}:`,
        error,
      );
      throw error;
    }
  }
  async incr(key: string): Promise<number> {
    this.logger.log(`Attempting to incr key: '${key}'`);
    try {
      const result = await this.redis.incr(key);
      this.logger.log(`Successfully incr for key '${key}', new value: ${result}`);
      return result;
    } catch (error) {
      this.logger.error(`Error incr for key '${key}':`, error);
      throw error;
    }
  }

  // onModuleDestroy is not needed as nestjs-redis handles client closing
}

