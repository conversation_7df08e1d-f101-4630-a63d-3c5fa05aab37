import { Injectable } from '@nestjs/common';
import { RedisService } from './redis.service.js'; // ← 你的 RedisService 路径

@Injectable()
export class LockService {
  constructor(private readonly redis: RedisService) {}

  /** 返回 true=拿到锁 / false=已有锁 */
  async tryLock(key: string, ttl = 5): Promise<boolean> {
    // RedisService 暴露的底层客户端
    const ok = await (this.redis as any).redis.set(key, '1', 'EX', ttl, 'NX');
    return ok === 'OK';
  }

  async unlock(key: string): Promise<void> {
    await (this.redis as any).redis.del(key);
  }
}
