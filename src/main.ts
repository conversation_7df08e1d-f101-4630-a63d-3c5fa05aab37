import 'reflect-metadata';
import * as cryptoModule from 'crypto';

if (typeof global.crypto === 'undefined') {
  (global as any).crypto = {};
}
if (typeof (global as any).crypto.randomUUID === 'undefined') {
  (global as any).crypto.randomUUID = function() {
    return cryptoModule.randomUUID();
  };
}

import cookieParser from 'cookie-parser'; // Import cookie-parser
import { NestFactory } from '@nestjs/core'; // Removed HttpAdapterHost as it's no longer used here
import { ValidationPipe } from '@nestjs/common'; // Import ValidationPipe
import { AppModule } from './app.module.js';
import { NestExpressApplication } from '@nestjs/platform-express';
import { TaskLoggerService } from './common/services/task-logger.service.js'; // Adjust path

async function bootstrap() {

  
  // 使用全局日志器初始化应用
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    rawBody: true, // Correct way to enable rawBody
  });

  // With rawBody: true, NestJS handles making the raw body available.
  // We still need body parsers for NestJS to parse the body into DTOs or @Body().
  // The default JSON parser should work alongside rawBody.
  // Explicitly setting them is fine.
  app.useBodyParser('json', { type: 'application/json' });
  app.useBodyParser('text', { type: 'text/plain' });

  // Resolve a new instance of TaskLoggerService for bootstrap/application-level logs
  app.useLogger(await app.resolve(TaskLoggerService));

  app.useGlobalPipes(new ValidationPipe({
    transform: true, // Automatically transform payloads to DTO instances
    whitelist: true, // Automatically strip non-whitelisted properties
  }));

  // Configure cookie-parser middleware
  // It's crucial to use a strong, unique secret, ideally from environment variables.
  const cookieSecret = process.env.COOKIE_SECRET;
  if (!cookieSecret) {
    console.warn(
      'WARNING: COOKIE_SECRET environment variable is not set. Using a default, insecure secret for development. PLEASE SET A STRONG SECRET IN PRODUCTION!',
    );
  }
  app.use(cookieParser(cookieSecret || 'REPLACE_THIS_IN_PRODUCTION_WITH_A_VERY_STRONG_SECRET'));

  // Determine CORS configuration based on environment
  const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;

  app.enableCors({
    // In development mode, allow all origins
    // In production, only allow specific domains
    origin: isDevelopment ? true : [
      /^(https:\/\/([a-zA-Z0-9-]+\.)*889990\.xyz)$/,
      /^https:\/\/[a-zA-Z0-9-]+\.lan$/     
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true, // Important for cookies to be sent/received across domains
  });

  if (isDevelopment) {
    console.log('Running in development mode: CORS configured to allow all origins');
  }

  await app.listen(process.env.PORT ?? 3000);
  console.log(`Application is running on: ${await app.getUrl()}`); // Added for better startup log
}
bootstrap();
