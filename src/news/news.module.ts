import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SearchController } from './search/search.controller.js';
import { NewsController } from './news.controller.js';
import { NewsAggregatorService } from './news-aggregator.service.js'; // Renamed from FetchService
import { V2exFetchService } from './fetch/v2ex-fetch.service.js';
import { ToutiaoFetchService } from './fetch/toutiao-fetch.service.js'; // Import ToutiaoFetchService
import { DouyinFetchService } from './fetch/douyin-fetch.service.js'; // Added
import { BaiduFetchService } from './fetch/baidu-fetch.service.js'; // Added BaiduFetchService
import { HupuFetchService } from './fetch/hupu-fetch.service.js';
import { BilibiliFetchService } from './fetch/bilibili-fetch.service.js'; // Import BilibiliFetchService
import { ZhihuFetchService } from './fetch/zhihu-fetch.service.js'; // Import ZhihuFetchService
import { JuejinFetchService } from './fetch/juejin-fetch.service.js'; // Import JuejinFetchService
import { ItzhijiaFetchService } from './fetch/itzhijia-fetch.service.js'; // Import ItzhijiaFetchService
import { LinuxdoFetchService } from './fetch/linuxdo-fetch.service.js'; // Import LinuxdoFetchService
import { WeiboFetchService } from './fetch/weibo-fetch.service.js'; // Import WeiboFetchService
import { IfengFetchService } from './fetch/ifeng-fetch.service.js';
import { SearchService } from './search/search.service.js';
import { HttpModule } from '../common/http.module.js';
import { RedisModule } from '../redis/redis.module.js';
import { FetchModule } from '../fetch/fetch.module.js'; // Corrected: Import FetchModule from fetch.module
// We might need RedisService here if SearchService depends on it directly
// and it's not globally available or provided by a shared module.
// For now, assuming RedisService is available from AppModule or a global module.

@Module({
  imports: [
    RedisModule,
    FetchModule, // Add FetchModule here
    HttpModule,
  ],
  controllers: [SearchController, NewsController],
  providers: [
    SearchService,
    V2exFetchService,
    ToutiaoFetchService, // Add ToutiaoFetchService
    DouyinFetchService, // Added
    BaiduFetchService, // Added BaiduFetchService
    HupuFetchService,
    BilibiliFetchService, // Add BilibiliFetchService
    ZhihuFetchService, // Add ZhihuFetchService
    JuejinFetchService, // Add JuejinFetchService
    ItzhijiaFetchService, // Add ItzhijiaFetchService
    LinuxdoFetchService, // Add LinuxdoFetchService
    WeiboFetchService, // Add WeiboFetchService
    IfengFetchService,
    NewsAggregatorService, // Renamed from FetchService
  ],
})
export class NewsModule {}