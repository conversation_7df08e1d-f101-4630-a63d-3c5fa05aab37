import { <PERSON>, <PERSON>, Logger, HttpEx<PERSON>, HttpStatus, Param, Query } from '@nestjs/common';
import { NewsAggregatorService } from './news-aggregator.service.js';
import { NewsSourceDataWrapper, SourceKey, NewsItem } from './fetch/fetch.types.js';

@Controller('/news')
export class NewsController {
  private readonly logger = new Logger(NewsController.name);

  constructor(
    private readonly fetchService: NewsAggregatorService
  ) {}

  @Get('/refresh/:source')
  async fetchBySource(
    @Param('source') source: SourceKey,
  ): Promise<{ message: string; data: NewsSourceDataWrapper | null }> { // Updated return type
    this.logger.log(`Received request to fetch and store ${source} data.`);
    const result = await this.fetchService.fetchDataBySource(source);
    if (!result) {
      throw new HttpException(
        `Source ${source} is not supported or data could not be fetched.`,
        HttpStatus.BAD_REQUEST,
      );
    }
    this.logger.log(`Successfully fetched and stored ${source} data.`);
    return {
      message: `${source} data fetched and stored successfully.`,
      data: result,
    };

  }

  @Get('/refresh-all')
  async fetchAllSources(): Promise<{ message: string; data: (NewsSourceDataWrapper | null)[] }> {
    this.logger.log('Received request to fetch and store data for all sources.');

    const results = await this.fetchService.fetchAllAndStore();
    this.logger.log('Successfully fetched and stored data for all sources.');
    return {
      message: 'All sources data fetched and stored successfully.',
      data: results, // This now correctly reflects that it can be (NewsSourceDataWrapper | null)[]
    };
  }

  @Get('/data')
  async getAllDataFromCache(): Promise<NewsSourceDataWrapper[]> {
    this.logger.log('Received request to get all data from cache.');
    const data = await this.fetchService.getAllDataFromCache();
    if (Object.keys(data).length === 0) {
      this.logger.log('No data found in cache for any source.');
    }
    return data;

  }

  @Get('/data/:source')
  async getDataBySourceFromCache(
    @Param('source') source: SourceKey,
  ): Promise<NewsSourceDataWrapper | null> {
    this.logger.log(`Received request to get ${source} data from cache.`);
    const data = await this.fetchService.getDataFromCache(source);
    if (!data) {
      this.logger.log(`No data found in cache for source: ${source}. You might want to refresh it via /news/fetch/refresh/${source}`);
      // throw new HttpException(`No data found in cache for ${source}. Try refreshing.`, HttpStatus.NOT_FOUND);
    }
    return data;
  }

}
