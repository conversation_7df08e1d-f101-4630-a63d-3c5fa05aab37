import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service.js';
import { V2exFetchService } from './fetch/v2ex-fetch.service.js'; // V2exRedisData 已移除
import { NewsSourceDataWrapper } from './fetch/fetch.types.js'; // 导入 NewsSourceDataWrapper
import { ToutiaoFetchService } from './fetch/toutiao-fetch.service.js';
import { DouyinFetchService } from './fetch/douyin-fetch.service.js';
import { BaiduFetchService } from './fetch/baidu-fetch.service.js';
import { HupuFetchService } from './fetch/hupu-fetch.service.js';
import { BilibiliFetchService } from './fetch/bilibili-fetch.service.js'; // Import BilibiliFetchService
import { ZhihuFetchService } from './fetch/zhihu-fetch.service.js'; // Import ZhihuFetchService
import { JuejinFetchService } from './fetch/juejin-fetch.service.js'; // Import JuejinFetchService
import { ItzhijiaFetchService } from './fetch/itzhijia-fetch.service.js'; // Import ItzhijiaFetchService
import { LinuxdoFetchService } from './fetch/linuxdo-fetch.service.js'; // Import LinuxdoFetchService
import { WeiboFetchService } from './fetch/weibo-fetch.service.js'; // Import WeiboFetchService
import { IfengFetchService } from './fetch/ifeng-fetch.service.js';
import { SourceKey, NewsItem } from './fetch/fetch.types.js';
import { TaskLogic } from '../common/decorators/task-logic.decorator.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { TaskParam } from '../common/decorators/task-param.decorator.js';

// CustomHttpService class has been removed.

@Injectable()
export class NewsAggregatorService { // Renamed from FetchService
  private readonly logger = new Logger(NewsAggregatorService.name); // Updated logger name

  constructor(
    private readonly v2exFetchService: V2exFetchService,
    private readonly toutiaoFetchService: ToutiaoFetchService,
    private readonly douyinFetchService: DouyinFetchService,
    private readonly baiduFetchService: BaiduFetchService,
    private readonly hupuFetchService: HupuFetchService,
    private readonly bilibiliFetchService: BilibiliFetchService, // Inject BilibiliFetchService
    private readonly zhihuFetchService: ZhihuFetchService, // Inject ZhihuFetchService
    private readonly juejinFetchService: JuejinFetchService, // Inject JuejinFetchService
    private readonly itzhijiaFetchService: ItzhijiaFetchService, // Inject ItzhijiaFetchService
    private readonly linuxdoFetchService: LinuxdoFetchService, // Inject LinuxdoFetchService
    private readonly weiboFetchService: WeiboFetchService, // Inject WeiboFetchService
    private readonly ifengFetchService: IfengFetchService,
    private readonly redisService: RedisService,
  ) {}

  @TaskLogic(TaskLogicEnum.FETCH_DATA)
  async fetchDataBySource(@TaskParam("source") source: SourceKey): Promise<NewsSourceDataWrapper | null> {
    this.logger.log(`Fetching data for source: ${source}`);
    try {
      switch (source) {
        case SourceKey.V2EX:
          return await this.v2exFetchService.fetchDataAndStore();
        case SourceKey.TOUTIAO:
          return await this.toutiaoFetchService.fetchDataAndStore();
        case SourceKey.DOUYIN: // Added
          return await this.douyinFetchService.fetchDataAndStore(); // Added
        case SourceKey.BAIDU:
          return await this.baiduFetchService.fetchDataAndStore();
        case SourceKey.HUPU:
          return await this.hupuFetchService.fetchDataAndStore();
        case SourceKey.BILIBILI: // Add Bilibili case
          return await this.bilibiliFetchService.fetchDataAndStore();
        case SourceKey.ZHIHU: // Add Zhihu case
          return await this.zhihuFetchService.fetchDataAndStore();
        case SourceKey.JUEJIN: // Add Juejin case
          return await this.juejinFetchService.fetchDataAndStore();
        case SourceKey.ITZHIJIA: // Add Itzhijia case
          return await this.itzhijiaFetchService.fetchDataAndStore();
        case SourceKey.LINUXDO: // Add LinuxDo case
          return await this.linuxdoFetchService.fetchDataAndStore();
        case SourceKey.WEIBO: // Add Weibo case
          return await this.weiboFetchService.fetchDataAndStore();
        case SourceKey.IFENG:
          return await this.ifengFetchService.fetchDataAndStore();
        default:
          this.logger.warn(`Unsupported source: ${source}`);
          return null;
      }
    } catch (error) {
      this.logger.error(`Error fetching data for source ${source}:`, error.message, error.stack);
      throw error; // Re-throw to be handled by controller or global filter
    }
  }

  async fetchAllAndStore(): Promise<(NewsSourceDataWrapper | null)[]> { // Return type can include nulls
    this.logger.log('Fetching data for all sources.');
    const services = [
      this.v2exFetchService,
      this.toutiaoFetchService,
      this.douyinFetchService,
      this.baiduFetchService,
      this.hupuFetchService,
      this.bilibiliFetchService, // Add BilibiliFetchService to services array
      this.zhihuFetchService, // Add ZhihuFetchService to services array
      this.juejinFetchService, // Add JuejinFetchService to services array
      this.itzhijiaFetchService, // Add ItzhijiaFetchService to services array
      this.linuxdoFetchService, // Add LinuxdoFetchService to services array
      this.weiboFetchService, // Add WeiboFetchService to services array
      this.ifengFetchService,
    ];

    // Since BaseFetchService.fetchDataAndStore now handles its own errors and can return null,
    // we can directly call it.
    const fetchPromises = services.map(service => service.fetchDataAndStore());

    const results = await Promise.allSettled(fetchPromises);

    const processedResults: (NewsSourceDataWrapper | null)[] = [];
    const sourceOrder = [
      SourceKey.V2EX,
      SourceKey.TOUTIAO,
      SourceKey.DOUYIN,
      SourceKey.BAIDU,
      SourceKey.HUPU,
      SourceKey.BILIBILI, // Add BILIBILI to sourceOrder
      SourceKey.ZHIHU, // Add ZHIHU to sourceOrder
      SourceKey.JUEJIN, // Add JUEJIN to sourceOrder
      SourceKey.ITZHIJIA, // Add ITZHIJIA to sourceOrder
      SourceKey.LINUXDO, // Add LINUXDO to sourceOrder
      SourceKey.WEIBO, // Add WEIBO to sourceOrder
      SourceKey.IFENG,
    ];

    results.forEach((result, index) => {
      const source = sourceOrder[index];
      if (result.status === 'fulfilled') {
        // result.value can be NewsSourceDataWrapper or null
        if (result.value) {
          this.logger.log(`Successfully fetched data for source: ${source}`);
          processedResults.push(result.value);
        } else {
          // This case means fetchDataAndStore completed but returned null (e.g., empty data after filtering by base class)
          this.logger.log(`Data fetch for source ${source} completed but returned no data (or was filtered out).`);
          processedResults.push(null);
        }
      } else {
        // This case means an unexpected error occurred in fetchDataAndStore that wasn't caught by the base service's try/catch
        // Or the promise was rejected for other reasons. BaseFetchService should catch most errors.
        this.logger.error(`Failed to fetch data for source ${source}: ${result.reason?.message}`, result.reason?.stack);
        processedResults.push(null); // Represent failure as null in the results array
      }
    });
    return processedResults.filter(r => r !== null); // Optionally filter out nulls if the caller expects only successful non-empty results
    // Or return processedResults directly if the caller needs to know about all attempts:
    // return processedResults;
    // For now, let's return only successful non-empty results to match previous behavior more closely.
  }

  async getDataFromCache(source: SourceKey): Promise<NewsSourceDataWrapper | null> {
    const redisKey = source; // Assuming SourceKey enum values match redisKeys
    this.logger.log(`Fetching data from Redis for key: ${redisKey}`);
    const cachedData = await this.redisService.get(redisKey);
    if (cachedData) {
      this.logger.log(`Cache hit for key: ${redisKey}`);
      // Updated type to include DouyinRedisData and use NewsSourceDataWrapper
      const parsedData: NewsSourceDataWrapper = JSON.parse(cachedData);
      return parsedData;
    }
    this.logger.log(`Cache miss for key: ${redisKey}`);
    return null;
  }

  @TaskLogic(TaskLogicEnum.GET_NEWS_TOP)
  async getAllDataFromCache(): Promise<NewsSourceDataWrapper[]> {
    this.logger.log('Fetching all data from cache for all sources.');
    const sources = Object.values(SourceKey);
    const allCachedData: NewsSourceDataWrapper[] = [];

    for (const source of sources) {
      const data = await this.getDataFromCache(source);
      if (data) {
        allCachedData.push(data);
      }
    }
    this.logger.log(`Returning ${allCachedData.length} sources from cache.`);
    return allCachedData;
  }

}
