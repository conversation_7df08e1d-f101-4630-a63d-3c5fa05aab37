import { Controller, Get, Query, BadRequestException } from '@nestjs/common';
import { SearchService } from './search.service.js';
import { BaiduFetchService } from '../fetch/baidu-fetch.service.js';
import { RedisService } from '@liaoliaots/nestjs-redis';

@Controller('/news/search') // Changed base path to 'news/search' for clarity
export class SearchController {

  constructor(
    private readonly searchService: SearchService) {}

  @Get()
  async index(
    @Query('q') q: string
  ): Promise<any> {
    if (!q) {
      throw new BadRequestException('Query parameter "q" is required.');
    }
    const searchResult = await this.searchService.search(q);

    return searchResult;
  }

  @Get('/complete')
  async complete(@Query('q') q: string): Promise<string[]> {
    return this.searchService.complete(q);
  }
}
