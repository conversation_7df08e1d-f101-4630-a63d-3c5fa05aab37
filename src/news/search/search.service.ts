import { HttpService } from '@nestjs/axios';
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { firstValueFrom, map } from 'rxjs';
import { RedisService } from '../../redis/redis.service.js';
import * as cheerio from 'cheerio';
import { FetchService } from '../../fetch/fetch.service.js';
import { SourceKey, NewsSourceDataWrapper } from '../fetch/fetch.types.js';
import { RedisCache } from '../../common/decorators/redis-cache.decorator.js';

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly httpService: HttpService,
    private readonly fetchService: FetchService, // Added
  ) {}

  @RedisCache({ keyPattern: 'duckduckgo:{args[0]}' })
  async search(query: string): Promise<any> {
    const endpoint = 'https://html.duckduckgo.com/html/';
    const keyword = query;

    this.logger.log(`Fetching DuckDuckGo search results for query: "${keyword}"`);

    // DuckDuckGo expects form data as a string
    const postDataString = `q=${encodeURIComponent(keyword)}&b=`;

    const headers = {
      'Cache-Control': 'no-cache',
      'Content-Length': String(Buffer.byteLength(postDataString)), // Calculate Content-Length
      'Origin': 'https://html.duckduckgo.com',
      'Pragma': 'no-cache',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1',
      'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept-Language': `zh-CN,en-US;q=0.9,en;q=0.8`
    };

    try {
      // Note: HttpModule from @nestjs/axios must be imported in the module that provides SearchService (e.g., NewsModule).
      const response = await firstValueFrom(
        this.fetchService.post<string>(endpoint, postDataString, { headers }).pipe(map((res: import('axios').AxiosResponse<string>) => res.data))
      ) as string;
      this.logger.debug(`Received response from DuckDuckGo for query: "${keyword}"`);
      const $ = cheerio.load(response);

      const results: Array<{ title: string; link: string; description: string; }> = [];
      $('div.result').each((i, elem) => {
        const $elem = $(elem);
        const title = $elem.find('h2.result__title a').text().trim();
        let linkUrl = $elem.find('a.result__a').attr('href');
        if (linkUrl && linkUrl.startsWith('/l/')) {
          linkUrl = `https://duckduckgo.com${linkUrl}`;
        }

        const description = $elem.find('a.result__snippet').html() || "No description available";

        if (title && linkUrl) {
          results.push({
            title,
            link: linkUrl,
            description,
          });
        }
      });

      const paginList: Array<{ page: string | undefined; active: boolean; text: string }> = [];
      const relevantList: string[] = [];
      const totalText = '';
      const hintText = '';
      const origText = '';
      const adviceText = '';

      return {
        items: results,
        relevants: relevantList,
        total: totalText,
        hint: hintText,
        orig: origText,
        advice: adviceText,
        pagin: paginList,
      };

    } catch (error) {
      if (error.isAxiosError) { // Axios errors have a specific structure
        this.logger.error(`Axios error during DuckDuckGo search for query "${keyword}": ${error.message}`, error.stack);
        if (error.response) {
          this.logger.error(`DDG Response Status: ${error.response.status}`);
          this.logger.error(`DDG Response Data: ${JSON.stringify(error.response.data)}`);
          // Potentially throw a more specific HttpException based on error.response.status
          if (error.response.status === HttpStatus.FORBIDDEN) {
             throw new HttpException('Blocked by DuckDuckGo', HttpStatus.FORBIDDEN);
          }
        }
         throw new HttpException(`Failed to fetch from DuckDuckGo: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
      } else if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`Error during DuckDuckGo search for query "${keyword}": ${error.message}`, error.stack);
      return this.buildResultObject(keyword, endpoint);
    }
  }


  buildResultObject(keyword: string, url: string): any {
    const notResultHtml = `<div class="mnr-c"><div class="card-section"><p aria-level="3" role="heading" style="padding-top:.33em">找不到和您查询的“<span><em>${keyword}</em></span>”相匹配的内容或信息。</p><p style="margin-top:1em">建议：</p><ul style="margin-left:1.3em;margin-bottom:2em"><li>请检查输入字词有无错误。</li><li>请尝试其他查询词。</li><li>请改用较常见的字词。</li><li>请减少查询字词的数量。</li></ul></div></div>`;
    return {
      items: [],
      contentHtml: null,
      relevants: [],
      total: '',
      hint: '',
      orig: '',
      advice: '',
      pagin: [],
      notResult: notResultHtml,
      script: '',
      q: keyword,
      url: url,
    };
  }

  /**
   * 
   * @returns 默认
   */
  async searchTop(): Promise<string[]> {
    const cachedData = await this.redisService.get(SourceKey.BAIDU)
    if (cachedData) {
      this.logger.log(`Cache hit for key: ${SourceKey.BAIDU}`);
      const parsedData: NewsSourceDataWrapper = JSON.parse(cachedData);
      //随机10个
      return parsedData.data.map(item => item.title).sort(() => 0.5 - Math.random()).slice(0, 10);
    }
    return [];
  }


  async complete(query: string): Promise<string[]> {
    //百度搜索随机返回
    if(!query) {
      return this.searchTop();
    }
  
    const COMPLETE_URL = 'https://suggestqueries.google.com/complete/search?client=firefox';
    let keyword = query;
    if (keyword && keyword.length > 100) {
      keyword = keyword.substring(0, 100);
    }

    this.logger.log(`Fetching search completions for query: "${keyword}"`);

    try {
      const response = await firstValueFrom(
        this.httpService.get(`${COMPLETE_URL}&q=${encodeURIComponent(keyword)}`)
          .pipe(map(res => res.data))
      );

      // Parse the response - Google's completion API returns data in a specific format
      const items = response[1]?.map(item => item);

      if(!items || !items.length) {
        return this.searchTop();
      }

      return items;
    } catch (error) {
      this.logger.error(`Error fetching completions for "${keyword}": ${error.message}`, error.stack);
      return this.searchTop();
    }
  }
}
