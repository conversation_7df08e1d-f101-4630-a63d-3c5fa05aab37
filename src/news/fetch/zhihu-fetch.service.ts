import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js';

// API Response Interfaces for Zhihu
interface ZhihuApiTargetLink {
  url: string;
}

interface ZhihuApiTitleArea {
  text: string;
}

interface ZhihuApiMetricsArea {
  text: string; // e.g., "1813 万热度"
}

interface ZhihuApiExcerptArea {
  text: string;
}

interface ZhihuApiTarget {
  link: ZhihuApiTargetLink;
  title_area: ZhihuApiTitleArea;
  metrics_area: ZhihuApiMetricsArea;
  excerpt_area: ZhihuApiExcerptArea;
  // id: string; // Example, if there's an id
  // type: string; // Example
}

interface ZhihuApiDataItem {
  target: ZhihuApiTarget;
  // id: string; // Example
  // type: string; // Example
  // attached_info: string; // Example
  // common_params: any; // Example
}

interface ZhihuApiResponse {
  data: ZhihuApiDataItem[];
  // paging: any; // Example
  // fresh_text: string; // Example
}

@Injectable()
export class ZhihuFetchService extends BaseFetchService {
  private readonly zhihuHotListUrl = 'https://www.zhihu.com/api/v3/feed/topstory/hot-list-web?limit=50&desktop=true'; // Increased limit

  constructor(
    httpService: HttpService,
    redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(ZhihuFetchService.name));
  }

  protected get sourceKey(): SourceKey {
    return SourceKey.ZHIHU;
  }

  protected get sourceName(): string {
    return '知乎热榜';
  }

  protected get iconColor(): string {
    return '#0177D7'; // Zhihu blue
  }

  private transformZhihuItem(item: ZhihuApiDataItem): NewsItem {
    const idMatch = item.target.link.url.match(/(\d+)$/);
    const id = idMatch ? idMatch[1] : item.target.link.url;

    return {
      id: id,
      title: item.target.title_area.text,
      link: item.target.link.url,
      extra: item.target.metrics_area.text, // "1813 万热度"
      preview: item.target.excerpt_area.text,
      // date_published and date_modified are not available from this API
    };
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from Zhihu Hot List API: ${this.zhihuHotListUrl}`);
    
    const response = await firstValueFrom(
      this.httpService.get<ZhihuApiResponse>(this.zhihuHotListUrl, {
        headers: {
          // Zhihu API might require specific headers, e.g., User-Agent, Cookie
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          // Add other headers like 'Cookie' if necessary based on API requirements
        },
      }),
    );

    const apiResponseData = response.data;

    if (!apiResponseData || !apiResponseData.data || !Array.isArray(apiResponseData.data)) {
      const errorMsg = 'Failed to fetch or parse Zhihu hot list data, API response format unexpected.';
      this.logger.error(errorMsg, JSON.stringify(apiResponseData));
      throw new Error(errorMsg);
    }

    const rawItems: ZhihuApiDataItem[] = apiResponseData.data;
    this.logger.debug(`Successfully fetched ${rawItems.length} items from Zhihu.`);

    const transformedItems = rawItems.map(item => this.transformZhihuItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);
    
    return transformedItems;
  }
}