import { HttpService } from '@nestjs/axios'; // Keep for super, but will use CurlChrome116Tool
import { Injectable, Logger } from '@nestjs/common';
import { FetchService } from '../../fetch/fetch.service.js';
import { RedisService } from '../../redis/redis.service.js';
import { BaseFetchService } from './base-fetch.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class LinuxdoFetchService extends BaseFetchService {
  private static readonly categoryMap: Record<number, string> = {
    4: '开发调优',
    14: '资源荟萃',
    42: '文档共建',
    10: '跳蚤市场',
    27: '非我莫属',
    32: '读书成诗',
    46: '扬帆起航',
    34: '前沿快讯',
    92: '网络记忆',
    36: '福利羊毛',
    11: '搞七捻三',
    2: '运营反馈',
    45: '深海幽域',
  };

  protected get sourceKey(): SourceKey {
    return SourceKey.LINUXDO;
  }

  protected get sourceName(): string {
    return 'LINUX DO';
  }

  protected get iconColor(): string {
    return '#ccc';
  }

  constructor(
    // HttpService is still needed for the super() call, even if not directly used by this class's logic.
    // It could be a "dummy" or the actual HttpService if other parts of BaseFetchService might use it.
    httpService: HttpService,
    redisService: RedisService,
    private readonly fetchService: FetchService, // Inject CurlChrome116Tool
  ) {
    // The Logger instance for the base class is created here and passed to super.
    super(httpService, redisService, new Logger(LinuxdoFetchService.name));
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    
    const apiUrl = 'https://linux.do/latest.json?order=created';
    try {
      const response = await firstValueFrom(
        this.fetchService.get<string>(apiUrl), // Expecting a JSON string
      );
      const jsonData = JSON.parse(response.data);

      // Assuming the structure is { users: [], topic_list: { topics: [] } }
      const topics = jsonData?.topic_list?.topics || [];

      if (!Array.isArray(topics)) {
        this.logger.warn('Fetched data does not contain an array of topics.', jsonData);
        return [];
      }

      return topics
        .map((topic) => this.transformLinuxDoItem(topic)) // Pass users for creator lookup if needed
        .filter((item): item is NewsItem => item !== null);
    } catch (error) {
      this.logger.error(
        `Failed to fetch or parse JSON data from LinuxDo: ${error.message}`,
        error.stack,
      );
      if (error.isAxiosError && error.response) {
        this.logger.error(
          `Axios error details: Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`,
        );
      } else if (error instanceof SyntaxError) { // JSON.parse error
        this.logger.error(`JSON Parsing Error: ${error.message}`);
      }
      throw error;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private transformLinuxDoItem(item: any): NewsItem | null {
    try {
      const id = item.id;
      const title = item.title;
      // The slug in the example is "topic", which is not unique.
      // A direct link to a topic is typically /t/slug/id or just /t/id
      // Using /t/id for robustness as slug might not be the actual topic slug in this context.
      const link = `https://linux.do/t/${item.id}`;
      const createdAt = item.created_at; // e.g., "2025-05-27T00:38:27.543Z"
      const categoryId: number | undefined = item.category_id;
      const tags = item.tags;

      if (!id || !title || !link) {
        this.logger.warn('Skipping item due to missing id, title, or link:', item);
        return null;
      }

      let extraParts: string[] = [];
      const categoryName = categoryId ? LinuxdoFetchService.categoryMap[categoryId] : undefined;

      if (categoryName) {
        extraParts.push(` ${categoryName}`);
      }

  
      if (tags && tags.length) {
        extraParts.push(`${tags[0]}`);
      }
 
      const extra = extraParts.length > 0 ? extraParts.join(' | ') : undefined;
      // Use fancy_title for preview, fallback to title. No separate description in topic list.
      const preview = item.fancy_title || title;

      return {
        id: String(id),
        title: String(title),
        link: String(link),
        date_published: createdAt ? new Date(createdAt).valueOf() : undefined,
        extra,
        preview: String(preview),
      };
    } catch (e) {
      this.logger.error(`Error transforming LinuxDo item: ${e.message}`, item);
      return null;
    }
  }


}
