import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js';

// API Response Interfaces for Juejin
interface JuejinApiContent {
  content_id: string;
  title: string;
  // Add other fields if needed, e.g., view_count, digg_count, comment_count
  view_count?: number;
  digg_count?: number;
  comment_count?: number;
}

interface JuejinApiDataItem {
  content: JuejinApiContent;
  // author_user_info, category_info, tags etc. might be here
}

interface JuejinApiResponse {
  err_no: number;
  err_msg: string;
  data: JuejinApiDataItem[];
  // cursor, count, has_more etc. might be here
}

@Injectable()
export class JuejinFetchService extends BaseFetchService {
  private readonly juejinHotRankUrl = 'https://api.juejin.cn/content_api/v1/content/article_rank?category_id=1&type=hot&spider=0&limit=50'; // Increased limit

  constructor(
    httpService: HttpService,
    redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(JuejinFetchService.name));
  }

  protected get sourceKey(): SourceKey {
    return SourceKey.JUEJIN;
  }

  protected get sourceName(): string {
    return '稀土掘金';
  }

  protected get iconColor(): string {
    return '#1e80ff'; // Juejin blue
  }

  private formatJuejinExtra(item: JuejinApiDataItem): string {
    // Example: "4,212 阅读" or "4.2k 阅读" - API doesn't directly provide this formatted string
    // We'll use digg_count as a proxy for "extra" if available, or view_count
    if (item.content.digg_count) {
      return `${item.content.digg_count.toLocaleString()}`; // Simple number for now
    }
    if (item.content.view_count) {
      return `${item.content.view_count.toLocaleString()}`;
    }
    return '';
  }

  private transformJuejinItem(item: JuejinApiDataItem): NewsItem {
    const contentId = item.content.content_id;
    return {
      id: contentId,
      title: item.content.title,
      link: `https://juejin.cn/post/${contentId}`,
      extra: this.formatJuejinExtra(item), // Using digg_count or view_count
      // preview is not directly available, could use a snippet of content if fetched
    };
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from Juejin Hot Rank API: ${this.juejinHotRankUrl}`);
    
    const response = await firstValueFrom(
      this.httpService.get<JuejinApiResponse>(this.juejinHotRankUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          // Juejin API might have other specific headers
        },
      }),
    );

    const apiResponseData = response.data;

    if (apiResponseData.err_no !== 0 || !apiResponseData.data || !Array.isArray(apiResponseData.data)) {
      const errorMsg = `Failed to fetch or parse Juejin hot rank data, API response error or format unexpected. ErrNo: ${apiResponseData.err_no}, ErrMsg: ${apiResponseData.err_msg}`;
      this.logger.error(errorMsg, JSON.stringify(apiResponseData));
      throw new Error(errorMsg);
    }

    const rawItems: JuejinApiDataItem[] = apiResponseData.data;
    this.logger.debug(`Successfully fetched ${rawItems.length} items from Juejin.`);

    const transformedItems = rawItems.map(item => this.transformJuejinItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);
    
    return transformedItems;
  }
}