import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey, NewsSourceDataWrapper } from './fetch.types.js';
import { AxiosResponse } from 'axios';

// 假设 SourceKey.BAIDU 将在 fetch.types.ts 中定义
// enum SourceKey {
//   BAIDU = 'baidu',
//   // ... other keys
// }

interface BaiduApiCardContentItem {
  word: string;
  rawUrl: string;
  hotScore: string;
  desc: string;
  isTop?: boolean;
}

interface BaiduApiCard {
  content: BaiduApiCardContentItem[];
}

interface BaiduApiResponse {
  cards: BaiduApiCard[];
}

@Injectable()
export class BaiduFetchService {
  private readonly logger = new Logger(BaiduFetchService.name);
  private readonly redisKey = SourceKey.BAIDU; // 或者 'baidu'
  private readonly baiduHotBoardUrl = 'https://top.baidu.com/board?tab=realtime';

  constructor(
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
  ) {}

  private formatHotScore(hotScoreStr: string): string {
    const score = parseInt(hotScoreStr, 10);
    if (score >= 10000) {
      return (score / 10000).toFixed(0) + '万';
    }
    return score.toString();
  }

  private transformBaiduItem(item: BaiduApiCardContentItem): NewsItem {
    return {
      id: item.rawUrl,
      title: item.word,
      link: item.rawUrl.replace("https://m.baidu.com", "https://www.baidu.com"),
      extra: this.formatHotScore(item.hotScore),
      preview: item.desc,
    };
  }

  async fetchDataAndStore(): Promise<NewsSourceDataWrapper> {
    this.logger.log(`[${this.redisKey}] Starting to fetch data...`);
    try {
      const axiosResponse: AxiosResponse<string> = await this.httpService.axiosRef.get(
        this.baiduHotBoardUrl,
        { responseType: 'text' } // 确保以文本形式获取响应
      );
      const htmlContent = axiosResponse.data;
      this.logger.log(`[${this.redisKey}] Successfully fetched HTML content.`);

      const match = htmlContent.match(/<!--s-data:(.*?)-->/s);
      if (!match || !match[1]) {
        this.logger.error(`[${this.redisKey}] Could not find s-data in HTML content.`);
        throw new Error('Could not extract JSON data from Baidu HTML response');
      }

      const jsonString = match[1];
      let apiResponse: BaiduApiResponse;
      try {
        apiResponse = JSON.parse(jsonString);
        this.logger.log(`[${this.redisKey}] Successfully parsed JSON data.`);
      } catch (parseError) {
        this.logger.error(`[${this.redisKey}] Failed to parse JSON: ${parseError.message}`, parseError.stack);
        throw new Error('Failed to parse JSON data from Baidu response');
      }

      if (!apiResponse ||  !apiResponse.cards || apiResponse.cards.length === 0) {
        this.logger.error(`[${this.redisKey}] Invalid API response structure.`);
        throw new Error('Invalid API response structure from Baidu');
      }

      const rawItems = apiResponse.cards[0].content;
      const filteredItems = rawItems.filter(item => !item.isTop);
      const newsItems: NewsItem[] = filteredItems.map(item => this.transformBaiduItem(item));

      const redisData: NewsSourceDataWrapper = {
        name: '百度热搜榜',
        sourceKey: SourceKey.BAIDU, // Ensure SourceKey.BAIDU is used
        iconColor: '#2932E1',
        lastModifyTime: Date.now(),
        data: newsItems,
      };

      await this.redisService.set(this.redisKey, JSON.stringify(redisData));
      this.logger.log(`[${this.redisKey}] Successfully stored data in Redis.`);

      return redisData;
    } catch (error) {
      this.logger.error(`[${this.redisKey}] Error fetching or storing data: ${error.message}`, error.stack);
      throw error; // Re-throw the error to be handled by the caller
    }
  }
}
