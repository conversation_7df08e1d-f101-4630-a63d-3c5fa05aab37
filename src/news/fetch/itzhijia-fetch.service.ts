import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js';
import * as cheerio from 'cheerio';



@Injectable()
export class ItzhijiaFetchService extends BaseFetchService {
  private readonly itzhijiaListUrl = 'https://www.ithome.com/block/rank.html';

  constructor(
    httpService: HttpService,
    redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(ItzhijiaFetchService.name));
  }

  protected get sourceKey(): SourceKey {
    return SourceKey.ITZHIJIA;
  }

  protected get sourceName(): string {
    return 'IT之家';
  }

  protected get iconColor(): string {
    return '#D22222'; // IT之家 red
  }

  // Use Parameters to infer the type of 'el' from how it's used with Cheerio's $ function
  // This is a more robust way if direct type imports are problematic.
  // Assuming '$' is a CheerioAPI instance, its first argument type is what we need.
  // However, a simpler approach for now, if specific Element type is elusive:
  private transformItzhijiaItem(el: any, $: cheerio.CheerioAPI): NewsItem | null {
    const $a = $(el).find('a');
    // More specific selector for the title link within the list item
    const url = $a.attr('href');
    const title = $a.attr('title')?.trim();
    

    if (url && title) {
      const adKeywords = ["神券", "优惠", "补贴", "京东", "广告", "推广", "特惠", "红包"];
      const isAd = url.includes("lapin") || url.includes("go.ithome.com") || adKeywords.some(k => title.includes(k));
      if (isAd) {
        this.logger.debug(`Filtered out ad: ${title} (${url})`);
        return null;
      }

      // Ensure URL is absolute
      const absoluteUrl = url.startsWith('http') ? url : (url.startsWith('//') ? `https:${url}` : `https://www.ithome.com${url.startsWith('/') ? '' : '/'}${url}`);
      
      return {
        id: absoluteUrl,
        title: title,
        link: absoluteUrl,
        extra: ''
      };
    }
    this.logger.warn(`Could not parse item. URL: ${url}, Title: ${title}`);
    return null;
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from IT之家 list page: ${this.itzhijiaListUrl}`);
    
    const response = await firstValueFrom(
      this.httpService.get<string>(this.itzhijiaListUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
          'Connection': 'keep-alive',
        },
      }),
    );

    const html = response.data;
    const $ = cheerio.load(html);
    
    const newsItems: NewsItem[] = [];
    // Try a more general selector for list items, then refine if needed
    $('#d-1 > li').each((_index, element) => {
      const item = this.transformItzhijiaItem(element, $);
      if (item) {
        newsItems.push(item);
      }
    });

    if (newsItems.length === 0) {
        this.logger.warn(`No items parsed from IT之家. HTML structure might have changed. Body length: ${html.length}`);
    } else {
        this.logger.debug(`Successfully parsed ${newsItems.length} items from IT之家.`);
    }
        
    return newsItems.sort((a, b) => (b.date_published ?? 0) - (a.date_published ?? 0));
  }
}