import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { RedisService } from '../../redis/redis.service.js';
import { BaseFetchService } from './base-fetch.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';

@Injectable()
export class IfengFetchService extends BaseFetchService {
  constructor(
    protected readonly httpService: HttpService,
    protected readonly redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(IfengFetchService.name));
  }

  get sourceKey(): SourceKey {
    return SourceKey.IFENG;
  }

  get sourceName(): string {
    return '凤凰资讯';
  }

  get iconColor(): string {
    return '#f54343';
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    const newsItems: NewsItem[] = [];
    try {
      const response = await firstValueFrom(
        this.httpService.get<string>('https://www.ifeng.com/', {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            Accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Cache-Control': 'max-age=0',
          },
          timeout: 15000, // 15 seconds timeout
        }),
      );
      const html = response.data;
      const regex = /var\s+allData\s*=\s*(\{[\s\S]*?\});/;
      const match = html.match(regex);

      if (match && match[1]) {
        const jsonDataString = match[1];
        const jsonData = JSON.parse(jsonDataString);

        const hotNewsList = jsonData?.hotNews1;

        if (Array.isArray(hotNewsList)) {
          for (const item of hotNewsList as { url: string; title: string; newsTime: string }[]) {
            if (!item.url || !item.title) {
              this.logger.warn(`Skipping item with missing URL or title: ${JSON.stringify(item)}`, this.constructor.name);
              continue;
            }

            const newsItem: NewsItem = {
              id: item.url,
              link: item.url,
              title: item.title,
            };

            if (item.newsTime) {
              let dateStr = item.newsTime;
              let parsedDate: Date;

              // 检查是否是 "MM/DD HH:mm" 格式
              if (/^\d{1,2}\/\d{1,2}\s\d{1,2}:\d{1,2}$/.test(dateStr)) {
                const currentYear = new Date().getFullYear();
                const parts = dateStr.split(' ')[0].split('/');
                const timePart = dateStr.split(' ')[1];
                const month = parts[0].padStart(2, '0');
                const day = parts[1].padStart(2, '0');
                parsedDate = new Date(`${currentYear}/${month}/${day} ${timePart}`);
              } else if (/^\d{4}[\/-]\d{1,2}[\/-]\d{1,2}\s\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(dateStr)) {
                // 尝试直接解析 "YYYY/MM/DD HH:mm:ss" 或 "YYYY-MM-DD HH:mm:ss" 或 "YYYY/MM/DD HH:mm"
                parsedDate = new Date(dateStr.replace(/-/g, '/'));
              } else {
                const timestamp = parseInt(dateStr, 10);
                if (!isNaN(timestamp) && (String(timestamp).length === 10 || String(timestamp).length === 13) ) {
                  // 假设时间戳可能是秒或毫秒。
                  parsedDate = new Date(timestamp * (String(timestamp).length === 10 ? 1000 : 1));
                } else {
                  parsedDate = new Date(NaN); // 无效日期
                }
              }

              if (parsedDate && !isNaN(parsedDate.getTime())) {
                newsItem.date_published = parsedDate.getTime();
              } else {
                this.logger.warn(
                  `Failed to parse newsTime: "${item.newsTime}" for item: "${item.title}". Resulting date: ${parsedDate}`,
                  this.constructor.name,
                );
              }
            }
            newsItems.push(newsItem);
          }
        } else {
          this.logger.warn(
            `hotNews1 is not an array or not found in JSON data. Path: jsonData.realData.hotNews1. jsonData keys: ${jsonData ? Object.keys(jsonData).join(', ') : 'null'}`,
            this.constructor.name,
          );
        }
      } else {
        this.logger.warn('Could not find allData JSON in HTML', this.constructor.name);
      }
    } catch (error) {
      this.logger.error(
        `Failed to fetch or process data from ifeng.com: ${error.message}`,
        error.stack,
        this.constructor.name,
      );
    }
    return newsItems;
  }
}
