import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js'; // NewsSourceDataWrapper 移除
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js'; // 导入 BaseFetchService

// 根据您提供的API响应定义接口
interface DouyinApiWordItem {
  sentence_id: string;
  word: string;
  hot_value: number;
  // 可能还有其他字段，根据需要添加
}

interface DouyinApiData {
  word_list: DouyinApiWordItem[];
  // 可能还有其他字段，根据需要添加
}

interface DouyinApiResponse {
  data: DouyinApiData;
  status_code: number;
  // 可能还有其他字段，根据需要添加
}

@Injectable()
export class DouyinFetchService extends BaseFetchService { // 继承 BaseFetchService
  // logger 和 redisKey 将由基类处理或不再直接需要
  private readonly cookieUrl = 'https://www.douyin.com/passport/general/login_guiding_strategy/?aid=6383';
  private readonly hotSearchListUrl = 'https://www.douyin.com/aweme/v1/web/hot/search/list/?device_platform=webapp&aid=6383&channel=channel_pc_web&detail_list=1';

  constructor(
    httpService: HttpService, // 不再是 readonly 成员
    redisService: RedisService, // 不再是 readonly 成员
  ) {
    super(httpService, redisService, new Logger(DouyinFetchService.name)); // 调用 super
  }

  protected get sourceKey(): SourceKey { // 实现抽象成员
    return SourceKey.DOUYIN;
  }

  protected get sourceName(): string { // 实现抽象成员
    return '抖音热榜';
  }

  protected get iconColor(): string { // 实现抽象成员
    return '#000000'; // 抖音黑色图标
  }

  private formatHotValue(hotValue: number): string {
    if (hotValue >= 10000) {
      return (hotValue / 10000).toFixed(1) + '万';
    }
    return hotValue.toString();
  }

  private transformDouyinItem(item: DouyinApiWordItem): NewsItem {
    return {
      id: item.sentence_id,
      title: item.word,
      link: `https://www.douyin.com/hot/${item.sentence_id}`,
      extra: this.formatHotValue(item.hot_value),
    };
  }

  // fetchDataAndStore 方法被移除，其功能由 BaseFetchService 提供
  // 核心逻辑移至 fetchSpecificData

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug('Fetching Douyin hot search data process started.');

    // 1. 获取 Cookie
    this.logger.debug(`Fetching cookie from: ${this.cookieUrl}`);
    const cookieResponse = await firstValueFrom( // Removed AxiosResponse type annotation
      this.httpService.get(this.cookieUrl, {
        // headers: { /* ... */ } // 如果需要特定头部
      })
    );
    
    const cookiesArray = cookieResponse.headers['set-cookie'];
    if (!cookiesArray || cookiesArray.length === 0) {
      this.logger.error('Failed to get set-cookie header from Douyin passport.');
      // 抛出错误，由基类捕获
      throw new Error('Failed to retrieve cookies from Douyin passport.');
    }
    const cookie = cookiesArray.join('; ');
    this.logger.debug('Successfully retrieved cookies.');

    // 2. 获取热搜数据
    this.logger.debug(`Fetching hot search list from: ${this.hotSearchListUrl}`);
    const hotSearchResponse = await firstValueFrom(
      this.httpService.get(this.hotSearchListUrl, { // Removed generic type from .get() as well
        headers: {
          'Cookie': cookie,
          // 'User-Agent': '...',
          // 'Referer': '...',
        },
      })
    );

    const apiResponseData = hotSearchResponse.data;

    if (apiResponseData.status_code !== 0 || !apiResponseData.data || !Array.isArray(apiResponseData.data.word_list)) {
      const errorMsg = 'Failed to fetch or parse Douyin hot search data, API response format unexpected or status_code not 0.';
      this.logger.error(errorMsg, JSON.stringify(apiResponseData));
      throw new Error(errorMsg);
    }

    const rawItems: DouyinApiWordItem[] = apiResponseData.data.word_list;
    this.logger.debug(`Successfully fetched ${rawItems.length} items from Douyin hot search.`);

    const transformedItems = rawItems.map(item => this.transformDouyinItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);

    // Redis存储和 NewsSourceDataWrapper 构建由基类处理
    return transformedItems;
  }
}