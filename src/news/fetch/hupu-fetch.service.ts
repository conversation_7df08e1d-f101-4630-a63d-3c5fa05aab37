import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js'; // NewsSourceDataWrapper 移除
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js'; // 导入 BaseFetchService

interface HupuApiItem {
  title: string;
  url: string;
  mobil_url?: string;
  hot: string;
  index?: number;
}

interface HupuApiResponse {
  success: boolean;
  name: string;
  subtitle: string;
  update_time: string;
  data: HupuApiItem[];
}

@Injectable()
export class HupuFetchService extends BaseFetchService { // 继承 BaseFetchService
  // logger 和 redisKey 将由基类处理或不再直接需要
  private readonly hupuHotListUrl = 'https://api.vvhan.com/api/hotlist/huPu';

  constructor(
    httpService: HttpService, // 不再是 readonly 成员
    redisService: RedisService, // 不再是 readonly 成员
  ) {
    super(httpService, redisService, new Logger(HupuFetchService.name)); // 调用 super
  }

  protected get sourceKey(): SourceKey { // 实现抽象成员
    return SourceKey.HUPU;
  }

  protected get sourceName(): string { // 实现抽象成员
    return '虎扑步行街';
  }

  protected get iconColor(): string { // 实现抽象成员
    return '#C50100'; // 虎扑主题色
  }

  private transformHupuItem(item: HupuApiItem): NewsItem {
    return {
      id: item.url,
      title: item.title,
      link: item.url,
      extra: item.hot,
      // preview: '', // API 未直接提供适合作为预览的短文本
    };
  }

  // fetchDataAndStore 方法被移除，其功能由 BaseFetchService 提供
  // 核心逻辑移至 fetchSpecificData

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from ${this.hupuHotListUrl}`);
    // try-catch for specific errors can be here, otherwise BaseFetchService handles general errors.
    const response = await firstValueFrom(
      this.httpService.get<HupuApiResponse>(this.hupuHotListUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        },
      }),
    );
    const apiResponse = response.data;

    if (!apiResponse || !apiResponse.success || !Array.isArray(apiResponse.data)) {
      const errorMsg = 'Failed to fetch or invalid data structure from Hupu API';
      this.logger.error(errorMsg, apiResponse);
      throw new Error(errorMsg); // 抛出错误，由基类捕获
    }

    this.logger.debug(`Successfully fetched ${apiResponse.data.length} items from Hupu API.`);

    const newsItems: NewsItem[] = apiResponse.data.map(item => this.transformHupuItem(item));

    // Redis存储和 NewsSourceDataWrapper 构建由基类处理
    return newsItems;
  }
}