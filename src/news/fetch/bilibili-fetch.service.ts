import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js';

// API Response Interfaces
interface BilibiliApiItem {
  keyword: string;
  show_name: string;
  heat_score: number; // Assuming heat_score is a number, adjust if it's string
  // icon: string; // Example, if there's an icon URL
  // position: number; // Example
}

interface BilibiliApiResponse {
  code: number;
  message: string;
  ttl: number;
  list: BilibiliApiItem[];
  // seid: string; // Example
  // exp_str: string; // Example
}


@Injectable()
export class BilibiliFetchService extends BaseFetchService {
  private readonly bilibiliHotwordUrl = 'https://s.search.bilibili.com/main/hotword?limit=50'; // Increased limit to 50 as per example data

  constructor(
    httpService: HttpService,
    redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(BilibiliFetchService.name));
  }

  protected get sourceKey(): SourceKey {
    return SourceKey.BILIBILI;
  }

  protected get sourceName(): string {
    return 'Bilibili';
  }

  protected get iconColor(): string {
    return '#0EA2E2'; // Bilibili blue
  }

  private transformBilibiliItem(item: BilibiliApiItem): NewsItem {
    return {
      id: item.keyword, // Using keyword as ID
      title: item.show_name,
      link: `https://search.bilibili.com/all?keyword=${encodeURIComponent(item.keyword)}`,
      extra: item.heat_score ? `${(item.heat_score / 10000).toFixed(1)}万` : '', // Formatting heat_score like others
      preview: item.keyword, // Using keyword as preview
      // date_published and date_modified are not available from this API
    };
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from Bilibili Hotword API: ${this.bilibiliHotwordUrl}`);
    
    const response = await firstValueFrom(
      this.httpService.get<BilibiliApiResponse>(this.bilibiliHotwordUrl, {
        // Bilibili API might require specific headers, e.g., User-Agent
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      }),
    );

    const apiResponseData = response.data;

    if (apiResponseData.code !== 0 || !apiResponseData.list || !Array.isArray(apiResponseData.list)) {
      const errorMsg = 'Failed to fetch or parse Bilibili hotword data, API response format unexpected or code not 0.';
      this.logger.error(errorMsg, JSON.stringify(apiResponseData));
      throw new Error(errorMsg);
    }

    const rawItems: BilibiliApiItem[] = apiResponseData.list;
    this.logger.debug(`Successfully fetched ${rawItems.length} items from Bilibili.`);

    const transformedItems = rawItems.map(item => this.transformBilibiliItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);
    
    return transformedItems;
  }
}