import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { SourceKey, NewsItem } from './fetch.types.js'; // NewsSourceDataWrapper 移除
import { RedisService } from '../../redis/redis.service.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js'; // 导入 BaseFetchService

// V2exRedisData 接口已移除
// 类型定义
export interface V2exApiItem {
  id: number | string;
  title: string;
  url: string;
  content_html: string;
  created: number;
  last_modified: number;
  member: {
    username: string;
  };
  node: {
    title: string;
  };
  date_modified?: number;
  date_published?: number;
  author?: { name: string };
  content?: string;
}

@Injectable()
export class V2exFetchService extends BaseFetchService { // 继承 BaseFetchService
  // logger 和 redisKey 将由基类处理或不再直接需要

  constructor(
    // HttpService 和 RedisService 不再是 readonly 成员，它们将被传递给 super()
    httpService: HttpService,
    redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(V2exFetchService.name)); // 调用 super
  }

  protected get sourceKey(): SourceKey { // 实现抽象成员
    return SourceKey.V2EX;
  }

  protected get sourceName(): string { // 实现抽象成员
    return 'V2EX';
  }

  protected get iconColor(): string { // 实现抽象成员
    return '#000';
  }

  private transformV2exItem(item: V2exApiItem): NewsItem {
    return {
      id: String(item.id),
      title: item.title,
      extra: item.node?.title || item.author?.name || '',
      link: item.url,
      preview: item.content_html || item.content || '',
      date_modified: item.last_modified || item.date_modified,
      date_published: item.created || item.date_published,
    };
  }

  // fetchDataAndStore 方法被移除，其功能由 BaseFetchService 提供
  // 核心逻辑移至 fetchSpecificData

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    const urls = [
      'https://www.v2ex.com/api/topics/hot.json',
      'https://www.v2ex.com/api/topics/latest.json',
    ];

    // this.logger is available from BaseFetchService if fine-grained logging is needed here
    this.logger.debug(`Fetching data from V2EX APIs: ${urls.join(', ')}`);

    // try-catch for specific errors related to V2EX fetching can be here,
    // otherwise, general errors will be caught by BaseFetchService.
    const responses = await Promise.all(
      urls.map(async (url) => {
        // this.httpService is inherited from BaseFetchService
        const response = await firstValueFrom(this.httpService.get<V2exApiItem[]>(url));
        return response.data;
      })
    );

    this.logger.debug(`Successfully fetched data from ${responses.length} V2EX endpoints.`);

    let allItems: V2exApiItem[] = [];
    responses.forEach((data, index) => {
      if (Array.isArray(data)) {
        allItems = allItems.concat(data);
        this.logger.debug(`Endpoint ${urls[index]} returned ${data.length} items.`);
      } else {
        // It's good practice to log or handle unexpected structures.
        this.logger.warn(`Endpoint ${urls[index]} did not return an array or expected data structure. Data: ${JSON.stringify(data)}`);
      }
    });

    this.logger.debug(`Total items fetched before transformation: ${allItems.length}`);

    const transformedItems = allItems.map(item => this.transformV2exItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);

    transformedItems.sort((a, b) => {
      const dateA = a.date_modified ?? a.date_published ?? 0;
      const dateB = b.date_modified ?? b.date_published ?? 0;
      return dateB - dateA; // 降序排序
    });
    this.logger.debug(`Data sorted. ${transformedItems.length} items.`);

    // Building NewsSourceDataWrapper and storing in Redis is handled by BaseFetchService.
    // This method should only return the transformed NewsItem array.
    return transformedItems;
  }
}
