import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Logger } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service.js';
import { NewsItem, NewsSourceDataWrapper, SourceKey } from './fetch.types.js';

export abstract class BaseFetchService {
  protected abstract get sourceKey(): SourceKey;
  protected abstract get sourceName(): string;
  protected abstract get iconColor(): string;
  protected abstract fetchSpecificData(): Promise<NewsItem[]>;

  protected constructor(
    protected readonly httpService: HttpService,
    protected readonly redisService: RedisService,
    protected readonly logger: Logger,
  ) {}


  public async fetchDataAndStore(): Promise<NewsSourceDataWrapper | null> {
    this.logger.log(`Fetching data for source: ${this.sourceName}`);
    try {
      const items = await this.fetchSpecificData();

      if (!items || items.length === 0) {
        this.logger.warn(
          `No data fetched for source ${this.sourceName}, skipping Redis store.`,
        );
        throw new Error(`Failed to fetch data from ${this.sourceName}: empty results`);
      }

      const redisData: NewsSourceDataWrapper = {
        name: this.sourceName,
        sourceKey: this.sourceKey,
        iconColor: this.iconColor,
        lastModifyTime: Date.now(),
        data: items,
      };

      await this.redisService.set(this.sourceKey, JSON.stringify(redisData));
      this.logger.log(
        `Successfully fetched and stored ${this.sourceName} data. Items: ${items.length}`,
      );
      return redisData;
    } catch (error) {
      this.logger.error(
        `Error in ${this.sourceName} fetch and store process:`,
        error.message,
        error.stack,
      );
      if (error.isAxiosError && error.response) {
        this.logger.error(
          `Axios error details: Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw new Error(
        `Failed to fetch data from ${this.sourceName}: ${error.message}`,
      );
    }
  }
}
