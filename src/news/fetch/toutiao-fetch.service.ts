import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { NewsItem, SourceKey } from './fetch.types.js'; // NewsSourceDataWrapper 移除
import { RedisService } from '../../redis/redis.service.js';
import { firstValueFrom } from 'rxjs';
import { BaseFetchService } from './base-fetch.service.js'; // 导入 BaseFetchService

// 定义从今日头条API获取的原始数据项结构
interface ToutiaoApiItem {
  Title: string;
  Url: string;
  HotValue?: string; // 可选，用于 extra 字段
  // 根据实际API返回结构，可能还有其他字段
}

@Injectable()
export class ToutiaoFetchService extends BaseFetchService { // 继承 BaseFetchService
  // logger 和 redisKey 将由基类处理或不再直接需要
  private readonly toutiaoHotBoardUrl = 'https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc';

  constructor(
    httpService: HttpService, // 不再是 readonly 成员
    redisService: RedisService, // 不再是 readonly 成员
  ) {
    super(httpService, redisService, new Logger(ToutiaoFetchService.name)); // 调用 super
  }

  protected get sourceKey(): SourceKey { // 实现抽象成员
    return SourceKey.TOUTIAO;
  }

  protected get sourceName(): string { // 实现抽象成员
    return '今日头条';
  }

  protected get iconColor(): string { // 实现抽象成员
    return '#FF0000'; // 示例红色
  }

  protected formatHotScore(hotScoreStr: string): string {
    const score = parseInt(hotScoreStr, 10);
    if (score >= 10000) {
      return (score / 10000).toFixed(0) + ' 万';
    }
    return score.toString();
  }

  private transformToutiaoItem(item: ToutiaoApiItem): NewsItem {
    return {
      // id: item.ClusterIdStr, // 假设原始数据中没有直接的 id，可以考虑使用 Url 作为唯一标识或生成一个
      title: item.Title,
      link: item.Url,
      extra: item.HotValue && this.formatHotScore(item.HotValue) || '', // 如果有热度值，则使用，否则为空字符串
      // 根据 NewsItem 的定义，可能还需要其他字段，暂时留空或设为默认值
    };
  }

  // fetchDataAndStore 方法被移除，其功能由 BaseFetchService 提供
  // 核心逻辑移至 fetchSpecificData

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.debug(`Fetching data from Toutiao Hot Board: ${this.toutiaoHotBoardUrl}`);

    // try-catch for specific errors can be here, otherwise BaseFetchService handles general errors.
    const axiosResponse = await firstValueFrom(
      // this.httpService is inherited from BaseFetchService
      this.httpService.get<{ data: ToutiaoApiItem[] }>(this.toutiaoHotBoardUrl)
    );

    const apiResponseData = axiosResponse.data;

    if (!apiResponseData || !apiResponseData.data || !Array.isArray(apiResponseData.data)) {
      const errorMsg = 'Failed to fetch or parse Toutiao data, response.data format unexpected.';
      this.logger.error(errorMsg, JSON.stringify(apiResponseData));
      throw new Error(errorMsg); // 抛出错误，由基类捕获
    }

    const rawItems: ToutiaoApiItem[] = apiResponseData.data;
    this.logger.debug(`Successfully fetched ${rawItems.length} items from Toutiao.`);

    const transformedItems = rawItems.map(item => this.transformToutiaoItem(item));
    this.logger.debug(`Total items after transformation: ${transformedItems.length}`);

    // Sorting logic can be kept if specific to this source
    // transformedItems.sort((a, b) => { /* 排序逻辑 */ });

    // Redis存储和 NewsSourceDataWrapper 构建由基类处理
    return transformedItems;
  }
}
