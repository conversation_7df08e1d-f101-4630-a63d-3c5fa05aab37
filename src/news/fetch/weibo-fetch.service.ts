import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { RedisService } from '../../redis/redis.service.js';
import { BaseFetchService } from './base-fetch.service.js';
import { NewsItem, SourceKey } from './fetch.types.js';

// 微博API响应数据结构
interface WeiboRealtimeItem {
  word: string;
  flag_desc?: string;
  label_name?: string;
  is_ad?: number; // 假设广告条目有此字段
  num: number, //热度
  // 其他可能的字段，根据实际API响应添加
  [key: string]: any;
}

interface WeiboApiResponse {
  data: {
    realtime: WeiboRealtimeItem[];
    // 其他可能的字段
    [key: string]: any;
  };
  // 其他可能的顶层字段
  [key: string]: any;
}

@Injectable()
export class WeiboFetchService extends BaseFetchService {
  protected get sourceKey(): SourceKey {
    return SourceKey.WEIBO;
  }
  protected get sourceName(): string {
    return '微博热搜';
  }
  protected get iconColor(): string {
    return '#FF8200'; // 微博橙色
  }

  private formatHotValue(hotValue: number): string {
    if (hotValue >= 10000) {
      return (hotValue / 10000).toFixed(1) + '万';
    }
    return hotValue.toString();
  }

  private readonly WEIBO_HOT_SEARCH_URL = 'https://weibo.com/ajax/side/hotSearch';

  constructor(
    protected readonly httpService: HttpService,
    protected readonly redisService: RedisService,
  ) {
    super(httpService, redisService, new Logger(WeiboFetchService.name));
  }

  protected async fetchSpecificData(): Promise<NewsItem[]> {
    this.logger.log(`Fetching data from ${this.WEIBO_HOT_SEARCH_URL}`);
    try {
      const response = await firstValueFrom(
        this.httpService.get<WeiboApiResponse>(this.WEIBO_HOT_SEARCH_URL, {
          headers: {
            // 微博API可能需要特定的User-Agent或其他headers
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
        }),
      );

      const realtimeData = response.data?.data?.realtime;

      if (!realtimeData || !Array.isArray(realtimeData)) {
        this.logger.warn('No realtime data found in Weibo API response or data is not an array.');
        return [];
      }

      const processedData = realtimeData
        .filter(item => !item.is_ad) // 过滤掉广告
        .map((k: WeiboRealtimeItem) => ({
          id: k.word,
          title: k.word,
          extra: this.formatHotValue(k.num) + (k.flag_desc || k.label_name ? " " + (k.flag_desc || k.label_name) : ""),
          link: `https://s.weibo.com/weibo?q=${encodeURIComponent(k.word)}`,
        }));

      this.logger.log(`Successfully fetched and processed ${processedData.length} items from Weibo.`);
      return processedData;
    } catch (error) {
      this.logger.error(
        `Error fetching or processing Weibo data: ${error.message}`,
        error.stack,
      );
      if (error.isAxiosError && error.response) {
        this.logger.error(
          `Axios error details: Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`,
        );
      }
      // 根据 BaseFetchService 的错误处理，这里应该重新抛出错误或返回空数组
      // 为保持一致性，这里不直接抛出，让上层处理，或者根据项目统一错误处理策略调整
      return []; // 或者 throw error;
    }
  }
}