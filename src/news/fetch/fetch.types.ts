
// 通用的新闻源数据包装器
export interface NewsSourceDataWrapper {
  name: string;
  sourceKey: SourceKey;
  iconColor: string;
  lastModifyTime: number;
  data: NewsItem[];
}

// 通用的新闻条目格式
export interface NewsItem {
  id?: string; // id 是可选的，因为头条数据可能没有直接的id
  title: string;
  extra?: string;
  link: string;
  preview?: string; // preview 是可选的
  date_modified?: number;
  date_published?: number;
}

// 定义支持的数据源标识
export enum SourceKey {
  V2EX = 'v2ex',
  TOUTIAO = 'toutiao',
  DOUYIN = 'douyin', // 添加抖音数据源
  BAIDU = 'baidu',
  HUPU = 'hupu', // 添加虎扑数据源
  BILIBILI = 'bilibili', // 添加 Bilibili 数据源
  ZHIHU = 'zhihu', // 添加知乎数据源
  JUEJIN = 'juejin', // 添加稀土掘金数据源
  ITZHIJIA = 'itzhijia', // 添加IT之家数据源
  LINUXDO = 'linuxdo',
  WEIBO = 'weibo', // 新增微博
  IFENG = 'ifeng',
}
