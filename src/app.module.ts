import { <PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core'; // Import APP_INTERCEPTOR and APP_FILTER
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller.js';
import { AppService } from './app.service.js';
import { AuthGuard } from './common/guards/auth.guard.js';
import { TransformInterceptor } from './common/interceptors/transform.interceptor.js'; // Import TransformInterceptor
import { HttpExceptionFilter } from './common/filters/http-exception.filter.js'; // Import HttpExceptionFilter
import { RedisController } from './redis/redis.controller.js';
import { RedisService } from './redis/redis.service.js'; // Import our custom RedisService
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { NewsModule } from './news/news.module.js'; // Import NewsModule
import { AgentModule } from './agent/agent.module.js'; // Import AgentModule
import { NavModule } from './nav/nav.module.js'; // Import NavModule
import { MetaModule } from './meta/meta.module.js';
import { BondModule } from './bond/bond.module.js';
import { GithubModule } from './github/github.module.js';
import { MonitorModule } from './monitor/monitor.module.js';
import { LinkModule } from './link/link.module.js'; // Import LinkModule
import { AuthModule } from './auth/auth.module.js'; // Import AuthModule
import { FetchModule } from './fetch/fetch.module.js'; // Import FetchModule
import { TaskModule } from './task/task.module.js';
import { CommonModule } from './common/common.module.js'; // Import CommonModule
import { NoteModule } from './note/note.module.js';
import { HttpModule } from './common/http.module.js';


@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    HttpModule,
    ScheduleModule.forRoot(),
    CommonModule, // Add CommonModule here
    NewsModule, // Add NewsModule here
    AgentModule, // Add AgentModule here
    NavModule, // Add NavModule here
    MetaModule, // Add MetaModule here
    BondModule,
    GithubModule, // Add GithubModule here
    MonitorModule, // Add MonitorModule here
    AuthModule, // Add AuthModule here
    FetchModule, // Add FetchModule here
    LinkModule,
    TaskModule,
    NoteModule,
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const logger = new Logger('RedisModuleSetup');
        const host = configService.get<string>('REDIS_SERVER_HOST', 'redis');
        const port = configService.get<number>('REDIS_SERVER_PORT', 6379);
        const password = configService.get<string>('REDIS_SERVER_PASSWORD');
        logger.log(`Configuring Redis with host: ${host}, port: ${port}`);
        return {
          config: {
            host,
            port,
            password,
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [AppController, RedisController],
  providers: [
    AppService,
    AuthGuard,
    RedisService, // Add RedisService to providers
    TransformInterceptor, // Add TransformInterceptor as a provider
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    HttpExceptionFilter, // Add HttpExceptionFilter as a provider
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter, // Use it as a global filter
    }
  ],
})
export class AppModule {}
