// src/meta/dto/subscription-detail.dto.ts
import { IsString, IsUrl, IsOptional, IsInt, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { TrafficInfoDto } from './traffic-info.dto.js';

export class SubscriptionDetailDto {
  @IsUrl()
  url: string;

  @IsString()
  domain: string;

  @IsString()
  type: string; // e.g., 'base64', 'yaml'

  @IsUrl()
  @IsOptional()
  web_page?: string;

  @ValidateNested()
  @Type(() => TrafficInfoDto)
  traffic: TrafficInfoDto;

  @IsInt()
  @IsOptional()
  expiry?: number; // Unix timestamp

  @IsString()
  @IsOptional()
  raw_content_preview?: string;
}