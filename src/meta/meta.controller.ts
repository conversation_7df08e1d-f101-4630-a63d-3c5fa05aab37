// src/meta/meta.controller.ts
import { Controller, Get, Logger, Query, UseGuards, Res } from '@nestjs/common';
import { Response } from 'express';
import { MetaService } from './meta.service.js';
import { SubscriptionDetailDto } from './dto/subscription-detail.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js'; // Assuming this is your existing AuthGuard
import { AuthPermissions } from '../common/decorators/auth.decorator.js'; // Assuming this decorator exists for AuthGuard
import { PermissionLevel } from '../common/decorators/auth.enum.js'; // Assuming this enum exists
import { RawResponse } from '../common/decorators/raw-response.decorator.js';

@Controller('/meta')
export class MetaController {
  private readonly logger = new Logger(MetaController.name);

  constructor(private readonly metaService: MetaService) {}


  @Get('/combine')
  @RawResponse()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async combineSubscriptions(
    @Res({ passthrough: true }) res: Response,
  ): Promise<string> {
    this.logger.log('Endpoint /meta/combine called');
    const result = await this.metaService.combineSubscriptionsLogic();
    res.type('text/plain');
    return result;
  }

  @Get('/combine_yaml')
  @RawResponse()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async combineYamlSubscriptions(
    @Res({ passthrough: true }) res: Response,
  ): Promise<string> {
    this.logger.log('Endpoint /meta/combine_yaml called');
    const result = await this.metaService.combineYamlSubscriptionsLogic();
    res.type('text/plain');
    return result;
  }

  @Get('/subscription/details')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getSubscriptionDetails(
    @Query('url') url: string,
  ): Promise<SubscriptionDetailDto> {
    this.logger.log(`Endpoint /meta/subscription/details called with URL: ${url}`);
    return this.metaService.getSubscriptionDetailsLogic(url);
  }

  @Get('/subscriptions/all')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getAllSubscriptions(): Promise<Array<{ domain: string | null; url: string; type: string }>> {
    this.logger.log('Endpoint /meta/subscriptions/all called');
    return this.metaService.getAllSubscriptionsLogic();
  }

}
