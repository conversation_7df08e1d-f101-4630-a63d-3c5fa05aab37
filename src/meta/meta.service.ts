// src/meta/meta.service.ts
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../redis/redis.service.js';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import axios, { AxiosResponseHeaders, RawAxiosResponseHeaders } from 'axios';
import * as yaml from 'js-yaml';
import { URL } from 'url';
import { TrafficInfoDto } from './dto/traffic-info.dto.js';
import { SubscriptionDetailDto } from './dto/subscription-detail.dto.js';

const REDIS_KEY_AIRPORT = "airport";
const REDIS_KEY_AIRPORT_YAML = "airport_yaml";
const FETCH_TIMEOUT = 30000; // 30 seconds in milliseconds

@Injectable()
export class MetaService {
  private readonly logger = new Logger(MetaService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {
    this.logger.log('MetaService initialized');
  }

  private async _fetchUrlsFromRedis(redisKey: string, keyDescriptionForLog: string): Promise<string[]> {
    try {
      const data = await this.redisService.get(redisKey);
      if (data === null) {
        this.logger.error(`Key '${redisKey}' (${keyDescriptionForLog}) not found in Redis`);
        // Consistent with previous logic, throw an error.
        // Consider if a NotFoundException from @nestjs/common is more appropriate for controller handling.
        throw new Error(`Key '${redisKey}' (${keyDescriptionForLog}) not found in Redis. Please set it first.`);
      }
      const urls = data.split('\n').map(url => url.trim()).filter(url => url.length > 0);
      this.logger.log(`Found ${urls.length} ${keyDescriptionForLog} URLs in Redis for key '${redisKey}'`);
      return urls;
    } catch (e) {
      // Log the specific error and rethrow a generic or more specific error for the caller
      this.logger.error(`Error fetching ${keyDescriptionForLog} URLs from Redis (key: ${redisKey}): ${e.message}`, e.stack);
      // If 'e' is already an Error instance from the 'data === null' check, rethrow it.
      // Otherwise, wrap it or throw a new generic error.
      if (e instanceof Error && e.message.startsWith(`Key '${redisKey}'`)) {
        throw e;
      }
      throw new Error(`Failed to fetch ${keyDescriptionForLog} URLs from Redis: ${e.message}`);
    }
  }

  async fetchUrlContent(url: string, isYaml: boolean = false): Promise<{ content: string; headers: Record<string, string | string[]> } | null> {
    this.logger.log(`Fetching content from ${url}${isYaml ? ' (YAML)' : ''}`);
    try {
      const headers = {
        'User-Agent': isYaml ? 'Clash' : 'V2ray', // Simplified User-Agent
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
      };

      const response = await firstValueFrom(
        this.httpService.get(url, {
          timeout: FETCH_TIMEOUT,
          headers: headers,
          // Axios follows redirects by default up to a certain limit.
          // To prevent compressed responses that might be hard to decode if not handled:
          decompress: true, // Explicitly enable decompression by Axios
          responseType: 'text', // Ensure we get plain text
        }),
      );

      // Normalize headers (Axios headers can be complex)
      const normalizedHeaders: Record<string, string | string[]> = {};
      const rawHeaders = response.headers as RawAxiosResponseHeaders | AxiosResponseHeaders;

       if (rawHeaders) {
           Object.keys(rawHeaders).forEach(key => {
               const headerValue = rawHeaders[key];
               // AxiosResponseHeaders might already give string[], RawAxiosResponseHeaders might give string | string[]
               if (headerValue !== undefined) {
                normalizedHeaders[key.toLowerCase()] = headerValue;
               }
           });
       }


      if (response.status >= 400) {
        this.logger.warn(`HTTP error status: ${response.status} for ${url}. Response: ${response.data}`);
        if (response.status === 403 && typeof response.data === 'string' && response.data.toLowerCase().includes('cloudflare')) {
           this.logger.warn(`Detected Cloudflare protection for ${url}. Standard request methods might not work.`);
        }
        return null;
      }

      this.logger.log(`Successfully fetched content from ${url} with status ${response.status}`);
      return {
        content: response.data, // response.data should be the text content due to responseType: 'text'
        headers: normalizedHeaders,
      };
    } catch (error) {
      this.logger.error(`Error fetching ${url}: ${error.message}`, error.stack);
      if (axios.isAxiosError(error) && error.response?.data?.toLowerCase().includes('cloudflare')) {
           this.logger.warn(`Detected Cloudflare protection during error for ${url}.`);
      }
      return null;
    }
  }

  async fetchSourceUrls(): Promise<string[]> {
    // The specific error handling for this public method can be simpler now,
    // as the detailed logging and core logic are in the private method.
    try {
      return await this._fetchUrlsFromRedis(REDIS_KEY_AIRPORT, 'source');
    } catch (error) {
      // Log the high-level failure and rethrow or handle as appropriate for the public API.
      this.logger.error(`Failed to execute fetchSourceUrls: ${error.message}`, error.stack);
      // Re-throwing the original error from _fetchUrlsFromRedis or a new HttpException
      // For now, rethrow to maintain previous behavior. Consider HttpException for controller.
      throw error;
    }
  }

  async fetchYamlUrls(): Promise<string[]> {
    try {
      return await this._fetchUrlsFromRedis(REDIS_KEY_AIRPORT_YAML, 'YAML');
    } catch (error) {
      this.logger.error(`Failed to execute fetchYamlUrls: ${error.message}`, error.stack);
      throw error;
    }
  }

    async combineSubscriptionsLogic(): Promise<string> {
      try {
        const urls = await this.fetchSourceUrls();
        if (!urls.length) {
          this.logger.warn('No source URLs found for combineSubscriptionsLogic.');
          // Depending on requirements, could return empty string or throw specific error
        }
  
        const fetchPromises = urls.map(url => this.fetchUrlContent(url, false));
        const results = await Promise.all(fetchPromises);
  
        const validResults = results.filter(result => result !== null);
        if (validResults.length === 0) {
          throw new HttpException('Failed to fetch content from any URL', HttpStatus.INTERNAL_SERVER_ERROR);
        }
  
        const decodedContents = validResults.map(result => this.decodeIfBase64(result!.content));
        const combinedContent = decodedContents.join('\n');
        const finalBase64 = Buffer.from(combinedContent, 'utf-8').toString('base64');
  
        this.logger.log('Successfully combined and encoded non-YAML subscription content');
        return finalBase64;
      } catch (error) {
        this.logger.error(`Error in combineSubscriptionsLogic: ${error.message}`, error.stack);
        if (error instanceof HttpException) throw error;
        throw new HttpException(error.message || 'Failed to combine subscriptions', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  
    async combineYamlSubscriptionsLogic(): Promise<string> {
      try {
        const urls = await this.fetchYamlUrls();
         if (!urls.length) {
             this.logger.warn('No YAML URLs found for combineYamlSubscriptionsLogic.');
         }
  
        const fetchPromises = urls.map(url => this.fetchUrlContent(url, true));
        const results = await Promise.all(fetchPromises);
  
        const validResults = results.filter(result => result !== null);
        if (validResults.length === 0) {
          throw new HttpException('Failed to fetch content from any YAML URL', HttpStatus.INTERNAL_SERVER_ERROR);
        }
  
        const allProxies: any[] = [];
        for (const result of validResults) {
          try {
            const content = result!.content;
            const yamlData = yaml.load(content) as any; // Use 'any' or a more specific type if known
            if (yamlData && typeof yamlData === 'object' && yamlData.proxies && Array.isArray(yamlData.proxies)) {
              allProxies.push(...yamlData.proxies);
              this.logger.log(`Added ${yamlData.proxies.length} proxies from a YAML source.`);
            } else {
              this.logger.warn("A YAML source did not contain a 'proxies' array or was not in the expected format.");
            }
          } catch (e) {
            this.logger.error(`Error parsing YAML content: ${e.message}`, e.stack);
          }
        }
  
        if (allProxies.length === 0) {
          throw new HttpException('No valid proxies found in any YAML source', HttpStatus.INTERNAL_SERVER_ERROR);
        }
  
        const combinedYamlData = { proxies: allProxies };
        const yamlString = yaml.dump(combinedYamlData);
  
        this.logger.log(`Successfully combined ${allProxies.length} proxies from YAML sources`);
        return yamlString;
      } catch (error) {
        this.logger.error(`Error in combineYamlSubscriptionsLogic: ${error.message}`, error.stack);
        if (error instanceof HttpException) throw error;
        throw new HttpException(error.message || 'Failed to combine YAML subscriptions', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  
    async getSubscriptionDetailsLogic(url: string): Promise<SubscriptionDetailDto> {
      try {
        let parsedUrl: URL;
        try {
             parsedUrl = new URL(url);
        } catch (e) {
             throw new HttpException('Invalid URL format', HttpStatus.BAD_REQUEST);
        }
        const domain = parsedUrl.hostname;
  
        const result = await this.fetchUrlContent(url, true); // Assuming YAML flag might be relevant for headers
        if (!result) {
          throw new HttpException('Failed to fetch subscription content', HttpStatus.NOT_FOUND);
        }
  
        const headers = result.headers;
        const trafficInfo = new TrafficInfoDto();
        let expiry: number | undefined = undefined;
  
        const subscriptionUserInfoHeader = headers['subscription-userinfo'];
        if (typeof subscriptionUserInfoHeader === 'string') {
          const userInfo = this.parseSubscriptionUserinfo(subscriptionUserInfoHeader);
          trafficInfo.upload = userInfo.upload;
          trafficInfo.download = userInfo.download;
          trafficInfo.total = userInfo.total;
          if (trafficInfo.total > 0) {
            const used = trafficInfo.upload + trafficInfo.download;
            trafficInfo.remaining = Math.max(0, trafficInfo.total - used);
          }
          expiry = userInfo.expire;
        }
  
        const webPageHeader = headers['profile-web-page-url'];
        const web_page = typeof webPageHeader === 'string' ? webPageHeader : undefined;
        
        const contentPreview = result.content || ""; // Ensure it's a string
  
        const subscriptionDetail = new SubscriptionDetailDto();
        subscriptionDetail.url = url;
        subscriptionDetail.domain = domain;
        subscriptionDetail.type = this.isBase64(contentPreview) ? 'base64' : 'yaml'; // Or more sophisticated type detection
        subscriptionDetail.web_page = web_page;
        subscriptionDetail.traffic = trafficInfo;
        subscriptionDetail.expiry = expiry;
        subscriptionDetail.raw_content_preview = contentPreview; // Limit preview length
  
        return subscriptionDetail;
      } catch (error) {
        this.logger.error(`Error getting subscription details for ${url}: ${error.message}`, error.stack);
        if (error instanceof HttpException) throw error;
        throw new HttpException(error.message || 'Failed to get subscription details', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  
    async getAllSubscriptionsLogic(): Promise<Array<{ domain: string | null; url: string; type: string }>> {
      try {
        const base64UrlsPromise = this.fetchSourceUrls().catch(e => {
            this.logger.warn(`Failed to fetch base64 URLs for getAllSubscriptionsLogic: ${e.message}`);
            return []; // Return empty array on error to allow partial results
        });
        const yamlUrlsPromise = this.fetchYamlUrls().catch(e => {
            this.logger.warn(`Failed to fetch YAML URLs for getAllSubscriptionsLogic: ${e.message}`);
            return [];
        });
  
        const [base64Urls, yamlUrls] = await Promise.all([base64UrlsPromise, yamlUrlsPromise]);
  
        const allUrlsWithTypes: Array<{ url: string; type: string }> = [];
        base64Urls.forEach(url => allUrlsWithTypes.push({ url, type: 'base64' }));
        yamlUrls.forEach(url => allUrlsWithTypes.push({ url, type: 'yaml' }));
  
        if (allUrlsWithTypes.length === 0) {
          return [];
        }
  
        const subscriptionsSummary = allUrlsWithTypes.map(item => {
          let domain: string | null = null;
          try {
            domain = new URL(item.url).hostname;
          } catch (e) {
            this.logger.warn(`Invalid URL encountered in getAllSubscriptionsLogic: ${item.url}`);
          }
          return {
            domain,
            url: item.url,
            type: item.type,
          };
        });
  
        this.logger.log(`Successfully prepared summary for ${subscriptionsSummary.length} subscriptions`);
        return subscriptionsSummary;
      } catch (error) {
        this.logger.error(`Error in getAllSubscriptionsLogic: ${error.message}`, error.stack);
        if (error instanceof HttpException) throw error;
        throw new HttpException(error.message || 'Error fetching all subscriptions', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  
  public parseSubscriptionUserinfo(headerValue?: string): { upload: number; download: number; total: number; expire?: number } {
    const result = {
      upload: 0,
      download: 0,
      total: 0,
      expire: undefined as number | undefined,
    };

    if (!headerValue) {
      return result;
    }

    const pairs = headerValue.split(';');
    for (const pair of pairs) {
      const trimmedPair = pair.trim();
      if (!trimmedPair) {
        continue;
      }

      const keyValue = trimmedPair.split('=', 1); // Split only on the first '='
      if (keyValue.length !== 2) { // Should be exactly 2 parts
        const parts = trimmedPair.split('='); // Re-split for logging if format is unexpected
        if (parts.length !== 2) {
           this.logger.warn(`Malformed pair in Subscription-Userinfo: "${trimmedPair}"`);
           continue;
        }
        keyValue[0] = parts[0];
        keyValue[1] = parts.slice(1).join('='); // Reconstruct value if it contained '='
      }


      let [key, value] = keyValue;
      key = key.trim();
      value = value.trim();

      try {
        if (key === 'upload' || key === 'download' || key === 'total' || key === 'expire') {
          const numValue = parseInt(value, 10);
          if (isNaN(numValue)) {
            this.logger.warn(`Failed to parse numeric value for ${key}: ${value}`);
            continue;
          }
          if (key === 'expire') {
            result.expire = numValue;
          } else {
            result[key] = numValue;
          }
        }
      } catch (e) {
        this.logger.warn(`Failed to parse value for ${key}: ${value}`, e);
      }
    }
    return result;
  }

  public isBase64(content: string): boolean {
    if (!content || content.length === 0 || content.length % 4 !== 0) {
       // Basic checks: empty, not a multiple of 4 (unless padding is unusual)
       // A more robust check might be needed if various non-standard base64 encodings are expected.
       // The regex check is generally good for standard base64.
    }
    // Regex to check if string contains only Base64 characters (A-Z, a-z, 0-9, +, /, =)
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Regex.test(content)) {
      return false;
    }
    try {
      // Attempt to decode and re-encode. If it's valid base64,
      // the re-encoded string should match the original (considering padding).
      const decoded = Buffer.from(content, 'base64').toString('utf-8');
      // Check if the decoded string contains non-printable characters,
      // which might indicate it wasn't originally text. This is a heuristic.
      // eslint-disable-next-line no-control-regex
      if (/[\x00-\x08\x0B\x0C\x0E-\x1F]/.test(decoded) && !this.looksLikeXmlOrJson(decoded)) {
        // If it has control characters and doesn't look like structured data that might legitimately be base64 encoded
        // it's less likely to be intentional base64 text.
        // This is a heuristic, as binary data can also be base64 encoded.
      }
      const reEncoded = Buffer.from(decoded, 'utf-8').toString('base64');
      // Compare, allowing for differences in padding if the original had none but re-encoding added it.
      return content.replace(/=+$/, '') === reEncoded.replace(/=+$/, '');
    } catch (error) {
      return false;
    }
  }

  private looksLikeXmlOrJson(str: string): boolean {
    const trimmed = str.trim();
    return (trimmed.startsWith('<') && trimmed.endsWith('>')) ||
           (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
           (trimmed.startsWith('[') && trimmed.endsWith(']'));
  }

  public decodeIfBase64(content: string): string {
    if (this.isBase64(content)) {
      try {
        const decoded = Buffer.from(content, 'base64').toString('utf-8');
        this.logger.log('Successfully decoded base64 content');
        return decoded;
      } catch (e) {
        this.logger.error('Error decoding base64 content that passed isBase64 check', e);
        // Fallback to original content if decoding fails unexpectedly
      }
    }
    return content;
  }

  public formatBytesToGb(bytesValue: number): number {
    if (typeof bytesValue !== 'number' || isNaN(bytesValue)) {
      this.logger.warn(`Invalid input for formatBytesToGb: ${bytesValue}`);
      return 0;
    }
    const gbValue = bytesValue / (1024 * 1024 * 1024);
    return parseFloat(gbValue.toFixed(2)); // Ensures two decimal places
  }

  public formatExpiryDate(timestamp?: number): string | undefined {
    if (timestamp === null || timestamp === undefined || isNaN(timestamp)) {
      return undefined;
    }
    try {
      // Timestamps from subscription-userinfo are typically in seconds
      const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
      if (isNaN(date.getTime())) { // Check if date is valid
           this.logger.warn(`Invalid timestamp for formatExpiryDate: ${timestamp}`);
           return undefined;
      }
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (e) {
      this.logger.error(`Error formatting expiry timestamp ${timestamp}`, e);
      return undefined;
    }
  }


}