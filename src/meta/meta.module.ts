// src/meta/meta.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '../common/http.module.js';
import { MetaController } from './meta.controller.js';
import { MetaService } from './meta.service.js';
import { RedisModule } from '../redis/redis.module.js'; // We will likely need RedisModule from '../redis/redis.module' later
import { AuthModule } from '../auth/auth.module.js'; // ADD THIS IMPORT

@Module({
  imports: [
    HttpModule, // For making external HTTP requests in MetaService
    RedisModule, // Add this when MetaService needs RedisService
    AuthModule, // ADD AuthModule TO IMPORTS
  ],
  controllers: [MetaController],
  providers: [MetaService],
})
export class MetaModule {}