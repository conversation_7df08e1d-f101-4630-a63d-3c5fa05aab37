import { Injectable, Logger } from '@nestjs/common';
import { RedisCache } from './common/decorators/redis-cache.decorator.js';
import { RedisService } from './redis/redis.service.js';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(private readonly redisService: RedisService) {}

  getHello(): string {
    return 'Hello World!';
  }

  @RedisCache({ keyPattern: 'app_service:cached_data:{args[0]}' })
  async getCachedData(id: string): Promise<string> {
    this.logger.log(`[AppService] getCachedData called with id: ${id} - simulating data fetching...`);
    // 模拟耗时操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    return `This is data for id ${id}, fetched at ${new Date().toISOString()}`;
  }
}
