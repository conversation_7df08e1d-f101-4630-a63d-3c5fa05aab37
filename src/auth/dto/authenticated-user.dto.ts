// src/auth/dto/authenticated-user.dto.ts
export type AuthProvider = 'github' | 'linuxdo';

export interface AuthenticatedUser {
  id: string | number; // Provider's user ID
  sub?: string; // Subject identifier, often same as id
  username: string;
  login?: string; // Login name, often same as username
  name?: string; // Full name, often empty for linux.do
  displayName?: string; // Optional, might be same as username or name
  avatarUrl?: string;
  avatar_template?: string; // URL template for avatars
  email?: string; // Optional, may not always be available or desired
  active?: boolean; // User account status
  trust_level?: number;
  silenced?: boolean; // User silenced status
  external_ids?: any; // External identifiers, type can be more specific if known
  api_key?: string; // User's API key from provider (handle with care)
  provider: AuthProvider;
  providerAccessToken: string; // Store the token used to fetch this info
}