
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { IAuthStrategy } from './auth.strategy.interface.js';
import { AuthenticatedUser, AuthProvider } from '../dto/authenticated-user.dto.js';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class GithubAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(GithubAuthStrategy.name);
  private readonly GITHUB_OAUTH_CLIENT_ID: string;
  private readonly GITHUB_OAUTH_CLIENT_SECRET: string;
  private readonly GITHUB_OAUTH_CALLBACK_URL: string;
  private readonly GITHUB_OAUTH_AUTH_URL: string;
  private readonly GITHUB_OAUTH_TOKEN_URL: string;
  private readonly GITHUB_OAUTH_USER_API_URL: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.GITHUB_OAUTH_CLIENT_ID = this.configService.getOrThrow<string>('GITHUB_OAUTH_CLIENT_ID');
    this.GITHUB_OAUTH_CLIENT_SECRET = this.configService.getOrThrow<string>('GITHUB_OAUTH_CLIENT_SECRET');
    this.GITHUB_OAUTH_CALLBACK_URL = this.configService.getOrThrow<string>('GITHUB_OAUTH_CALLBACK_URL');
    this.GITHUB_OAUTH_AUTH_URL = this.configService.getOrThrow<string>('GITHUB_OAUTH_AUTH_URL');
    this.GITHUB_OAUTH_TOKEN_URL = this.configService.getOrThrow<string>('GITHUB_OAUTH_TOKEN_URL');
    this.GITHUB_OAUTH_USER_API_URL = this.configService.getOrThrow<string>('GITHUB_OAUTH_USER_API_URL');

    this.logger.log('GithubAuthStrategy initialized with new env config.');
  }

  getProviderName(): AuthProvider {
    return 'github';
  }

  getAuthUrl(state?: string): string {
    const params = new URLSearchParams({
      client_id: this.GITHUB_OAUTH_CLIENT_ID,
      redirect_uri: this.GITHUB_OAUTH_CALLBACK_URL,
      allow_signup: 'false',
      // scope: 'user:email read:user' // Add necessary scopes
    });
    if (state) {
      params.append('state', state);
    }
    return `${this.GITHUB_OAUTH_AUTH_URL}?${params.toString()}`;
  }

  async handleCallback(code: string, expressResponse: Response, state?: string ): Promise<AuthenticatedUser> {
    this.logger.debug(`GithubAuthStrategy: handleCallback with code: ${code}`);
    if (!code) {
       throw new HttpException('Authorization code missing', HttpStatus.BAD_REQUEST);
    }

    const postData = {
      client_id: this.GITHUB_OAUTH_CLIENT_ID,
      client_secret: this.GITHUB_OAUTH_CLIENT_SECRET,
      code: code,
      redirect_uri: this.GITHUB_OAUTH_CALLBACK_URL,
    };

    const tokenResponse = await firstValueFrom(
      this.httpService.post(this.GITHUB_OAUTH_TOKEN_URL, postData, {
        headers: { Accept: 'application/json' },
      }),
    );
    const tokenData = tokenResponse.data as any;
    this.logger.debug("github token resp: ", tokenData)
    if (tokenData.access_token) {
        const cookieOptions = { httpOnly: true, secure: this.configService.get<string>('NODE_ENV') === 'production', signed: true, path: '/' };
        expressResponse.cookie('gh_access_token', tokenData.access_token, { ...cookieOptions, maxAge: (tokenData.expires_in || 3600) * 1000 });
        if (tokenData.refresh_token) {
            expressResponse.cookie('gh_refresh_token', tokenData.refresh_token, { ...cookieOptions, maxAge: (tokenData.refresh_token_expires_in || 6 * 30 * 24 * 3600) * 1000 });
        }

        return await this.getUserInfo(tokenData.access_token);
    } else {
        throw new HttpException('Failed to obtain GitHub token', HttpStatus.UNAUTHORIZED);
    }

  }

  async getUserInfo(accessToken: string): Promise<AuthenticatedUser> {
    if (!accessToken) {
      throw new HttpException('Access token is missing', HttpStatus.UNAUTHORIZED);
    }
    const { data: githubUser } = await firstValueFrom(
      this.httpService.get(this.GITHUB_OAUTH_USER_API_URL, {
        headers: { Authorization: `Bearer ${accessToken}` },
      }),
    );
    if (!githubUser) {
      throw new HttpException('Failed to retrieve user information from GitHub', HttpStatus.UNAUTHORIZED);
    }
    return {
        id: githubUser.id.toString(),
        username: githubUser.login,
        displayName: githubUser.name || githubUser.login,
        avatarUrl: githubUser.avatar_url,
        email: githubUser.email,
        provider: 'github',
        providerAccessToken: accessToken,
    };
  
  }

  async refreshToken(refreshTokenValue: string, expressResponse: Response): Promise<AuthenticatedUser> {
    if (!refreshTokenValue) {
      this.logger.warn('GithubAuthStrategy: No refresh token provided for refresh attempt.');
      throw new HttpException('No refresh token provided', HttpStatus.UNAUTHORIZED);
    }
    const postData = new URLSearchParams({
      client_id: this.GITHUB_OAUTH_CLIENT_ID,
      client_secret: this.GITHUB_OAUTH_CLIENT_SECRET,
      grant_type: 'refresh_token',
      refresh_token: refreshTokenValue,
    });
    const tokenResponse = await firstValueFrom(
      this.httpService.post(this.GITHUB_OAUTH_TOKEN_URL, postData.toString(), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded', Accept: 'application/json' },
      }),
    );
    const tokenData = tokenResponse.data as any;
    if (tokenData.access_token) {
        const cookieOptions = { httpOnly: true, secure: this.configService.get<string>('NODE_ENV') === 'production', signed: true, path: '/' };
        expressResponse.cookie('gh_access_token', tokenData.access_token, { ...cookieOptions, maxAge: (tokenData.expires_in || 3600) * 1000 });
        if (tokenData.refresh_token) {
            expressResponse.cookie('gh_refresh_token', tokenData.refresh_token, { ...cookieOptions, maxAge: (tokenData.refresh_token_expires_in || 6 * 30 * 24 * 3600) * 1000 });
        }
      return await this.getUserInfo(tokenData.access_token);
    } else {
      this.logger.error('GithubAuthStrategy: Failed to refresh access token, no new token in response.', tokenData);
      this.clearCookies(expressResponse);
      throw new HttpException('Failed to refresh access token, no new token in response', HttpStatus.UNAUTHORIZED);
    }
  
  }

  clearCookies(expressResponse: Response): void {
     const cookieOptions = { path: '/', signed: true, httpOnly: true, secure: this.configService.get<string>('NODE_ENV') === 'production' };
     expressResponse.clearCookie('gh_access_token', cookieOptions);
     expressResponse.clearCookie('gh_refresh_token', cookieOptions);
  }
}
