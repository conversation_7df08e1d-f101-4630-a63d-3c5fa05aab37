// src/auth/strategies/auth.strategy.interface.ts
import { Response } from 'express';
import { AuthenticatedUser, AuthProvider } from '../dto/authenticated-user.dto.js';

export interface IAuthStrategy {
  /**
   * Gets the unique name of the authentication provider.
   */
  getProviderName(): AuthProvider;

  /**
   * Generates the authentication URL for the provider.
   * @param state Optional state parameter for OAuth2 flow.
   */
  getAuthUrl(state?: string): string;

  /**
   * Handles the OAuth callback from the provider.
   * Exchanges the authorization code for tokens and fetches user info.
   * Sets authentication cookies on the expressResponse.
   * @param code The authorization code from the provider.
   * @param expressResponse The express Response object to set cookies.
   * @param state Optional state parameter for verification.
   * @returns A Promise resolving to AuthenticatedUser. Throws HttpException on failure.
   */
  handleCallback(
    code: string,
    expressResponse: Response,
    state?: string,
  ): Promise<AuthenticatedUser>;

  /**
   * Fetches user information using a valid access token.
   * @param accessToken The access token for the provider.
   * @returns A Promise resolving to AuthenticatedUser. Throws HttpException on failure.
   */
  getUserInfo(accessToken: string): Promise<AuthenticatedUser>;

  /**
   * (Optional) Refreshes an access token if applicable for the provider.
   * @param refreshToken The refresh token.
   * @param expressResponse The express Response object to update cookies.
   * @returns A Promise resolving to the new AuthenticatedUser. Throws HttpException on failure.
   */
  refreshToken?(
     refreshTokenValue: string,
     expressResponse: Response
  ): Promise<AuthenticatedUser>;

  /**
   * Clears authentication cookies specific to this provider.
   * @param expressResponse The express Response object.
   */
  clearCookies(expressResponse: Response): void;
}
