// src/auth/strategies/linuxdo-auth.strategy.ts
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { IAuthStrategy } from './auth.strategy.interface.js';
import { AuthenticatedUser, AuthProvider } from '../dto/authenticated-user.dto.js';
import { firstValueFrom } from 'rxjs';

interface LinuxdoTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
  // other fields if any
}

@Injectable()
export class LinuxdoAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(LinuxdoAuthStrategy.name);
  private readonly LINUXDO_OAUTH_CLIENT_ID: string;
  private readonly LINUXDO_OAUTH_CLIENT_SECRET: string;
  private readonly LINUXDO_OAUTH_TOKEN_URL: string;
  private readonly LINUXDO_OAUTH_CALLBACK_URL: string;
  private readonly LINUXDO_OAUTH_AUTH_URL_BASE: string;
  private readonly LINUXDO_OAUTH_USER_INFO_URL: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.LINUXDO_OAUTH_CLIENT_ID = this.configService.getOrThrow<string>('LINUXDO_OAUTH_CLIENT_ID');
    this.LINUXDO_OAUTH_CLIENT_SECRET = this.configService.getOrThrow<string>('LINUXDO_OAUTH_CLIENT_SECRET');
    this.LINUXDO_OAUTH_TOKEN_URL = this.configService.getOrThrow<string>('LINUXDO_OAUTH_TOKEN_URL');
    this.LINUXDO_OAUTH_CALLBACK_URL = this.configService.getOrThrow<string>('LINUXDO_OAUTH_CALLBACK_URL'); // Use new env var
    this.LINUXDO_OAUTH_AUTH_URL_BASE = this.configService.getOrThrow<string>('LINUXDO_OAUTH_AUTH_URL');
    this.LINUXDO_OAUTH_USER_INFO_URL = this.configService.getOrThrow<string>('LINUXDO_OAUTH_USER_INFO_URL');

    if (!this.LINUXDO_OAUTH_CLIENT_ID || !this.LINUXDO_OAUTH_CLIENT_SECRET) {
        const errorMsg = 'Linux.do OAuth client_id or client_secret is not configured for LinuxdoAuthStrategy.';
        this.logger.error(errorMsg);
        throw new Error(errorMsg);
    }
    this.logger.log('LinuxdoAuthStrategy initialized with new env config.');
  }

  getProviderName(): AuthProvider {
    return 'linuxdo';
  }

  getAuthUrl(state?: string): string {
    const params = new URLSearchParams({
      client_id: this.LINUXDO_OAUTH_CLIENT_ID,
      redirect_uri: this.LINUXDO_OAUTH_CALLBACK_URL, // Use direct env var
      response_type: 'code',
    });
    if (state) {
      params.append('state', state);
    }
    return `${this.LINUXDO_OAUTH_AUTH_URL_BASE}?${params.toString()}`;
  }

  async handleCallback(
    code: string,
    expressResponse: Response,
    state?: string,
  ): Promise<AuthenticatedUser> {
    this.logger.debug(`LinuxdoAuthStrategy: handleCallback with code: ${code}`);
    if (!code) {
      throw new HttpException('Linux.do callback: Authorization code is missing.', HttpStatus.BAD_REQUEST);
    }

    const postData = new URLSearchParams({
      client_id: this.LINUXDO_OAUTH_CLIENT_ID,
      client_secret: this.LINUXDO_OAUTH_CLIENT_SECRET,
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: this.LINUXDO_OAUTH_CALLBACK_URL, // Use direct env var
    });

    const tokenResponse = await firstValueFrom(
      this.httpService.post<LinuxdoTokenResponse>(this.LINUXDO_OAUTH_TOKEN_URL, postData.toString(), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      }),
    );
    const tokenData = tokenResponse.data;

    if (tokenData.access_token) {
      const cookieOptions = {
        httpOnly: true,
        secure: this.configService.get<string>('NODE_ENV') === 'production',
        signed: true,
        path: '/',
      };
      expressResponse.cookie('ld_access_token', tokenData.access_token, {
        ...cookieOptions,
        maxAge: (tokenData.expires_in || 3600 * 24) * 1000,
      });
      if (tokenData.refresh_token) {
        expressResponse.cookie('ld_refresh_token', tokenData.refresh_token, {
          ...cookieOptions,
          maxAge: (30 * 24 * 3600) * 1000,
        });
      }
      this.logger.log('Linux.do access token and refresh token (if any) set in cookies by LinuxdoAuthStrategy.');
      return await this.getUserInfo(tokenData.access_token)
    } else {
      this.logger.error('Failed to obtain Linux.do access token in strategy.', tokenData);
      throw new HttpException('Failed to obtain Linux.do access token', HttpStatus.UNAUTHORIZED);
    }

  }

  async getUserInfo(accessToken: string): Promise<AuthenticatedUser> {
    if (!accessToken) {
      throw new HttpException('Access token is missing', HttpStatus.UNAUTHORIZED);
    }
    this.logger.debug('LinuxdoAuthStrategy: getUserInfo called');
    try {
      const { data: linuxdoUser } = await firstValueFrom(
        this.httpService.get(this.LINUXDO_OAUTH_USER_INFO_URL, {
          headers: { Authorization: `Bearer ${accessToken}` },
        }),
      );
      if (!linuxdoUser) {
        throw new HttpException('Failed to retrieve user information from Linux.do', HttpStatus.UNAUTHORIZED);
      }
      return {
        id: linuxdoUser.id, // Assuming id is the primary identifier
        sub: linuxdoUser.sub,
        username: linuxdoUser.username,
        login: linuxdoUser.login,
        name: linuxdoUser.name,
        displayName: linuxdoUser.name || linuxdoUser.username, // Prefer name if available, else username
        avatarUrl: linuxdoUser.avatar_url, // Prefer avatar_url from the example
        avatar_template: linuxdoUser.avatar_template,
        email: linuxdoUser.email,
        active: linuxdoUser.active,
        trust_level: linuxdoUser.trust_level,
        silenced: linuxdoUser.silenced,
        external_ids: linuxdoUser.external_ids,
        api_key: linuxdoUser.api_key, // Added api_key
        provider: 'linuxdo',
        providerAccessToken: accessToken,
      };
    } catch (error) {
      this.logger.error('LinuxdoAuthStrategy: Failed to fetch Linux.do user info:', error.response?.data || error.message);
      // Ensure the error message is a string
      const errorMessage = error.response?.data?.message || error.response?.data || error.message || 'Unknown error';
      throw new HttpException(`Failed to fetch Linux.do user info: ${String(errorMessage)}`, HttpStatus.UNAUTHORIZED);
    }
  }

  clearCookies(expressResponse: Response): void {
     const cookieOptions = { path: '/', signed: true, httpOnly: true, secure: this.configService.get<string>('NODE_ENV') === 'production' };
     expressResponse.clearCookie('ld_access_token', cookieOptions);
     expressResponse.clearCookie('ld_refresh_token', cookieOptions);
     this.logger.log('Cleared Linux.do auth cookies.');
  }

  async refreshToken(
    refreshTokenValue: string,
    expressResponse: Response,
  ): Promise<AuthenticatedUser> {
    this.logger.debug(`LinuxdoAuthStrategy: Attempting to refresh token.`);
    if (!refreshTokenValue) {
      this.logger.warn('LinuxdoAuthStrategy: No refresh token provided for refresh attempt.');
      throw new HttpException('No refresh token provided', HttpStatus.UNAUTHORIZED);
    }

    const postData = new URLSearchParams({
      client_id: this.LINUXDO_OAUTH_CLIENT_ID,
      client_secret: this.LINUXDO_OAUTH_CLIENT_SECRET,
      grant_type: 'refresh_token',
      refresh_token: refreshTokenValue,
    });

    
    const tokenResponse = await firstValueFrom(
      this.httpService.post<LinuxdoTokenResponse>(
        this.LINUXDO_OAUTH_TOKEN_URL,
        postData.toString(),
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
      ),
    );
    const tokenData = tokenResponse.data;

    if (tokenData.access_token) {
      this.logger.log('LinuxdoAuthStrategy: Successfully refreshed access token.');
      const cookieOptions = {
        httpOnly: true,
        secure: this.configService.get<string>('NODE_ENV') === 'production',
        signed: true,
        path: '/',
      };
      expressResponse.cookie('ld_access_token', tokenData.access_token, {
        ...cookieOptions,
        maxAge: (tokenData.expires_in || 3600 * 24) * 1000,
      });
      // Update refresh token if a new one is provided
      if (tokenData.refresh_token) {
        expressResponse.cookie('ld_refresh_token', tokenData.refresh_token, {
          ...cookieOptions,
          maxAge: (30 * 24 * 3600) * 1000, // Standard refresh token TTL
        });
        this.logger.debug('LinuxdoAuthStrategy: New refresh token also set.');
      }

      // Fetch user info with the new access token
      return this.getUserInfo(tokenData.access_token);
    } else {
      this.logger.error('LinuxdoAuthStrategy: Failed to refresh access token, no new token in response.', tokenData);
      // If refresh fails, it's good practice to clear the invalid refresh token
      this.clearCookies(expressResponse); // Or at least clear ld_refresh_token
      throw new HttpException('Failed to refresh access token, no new token in response', HttpStatus.UNAUTHORIZED);
    }
  
  }
}
