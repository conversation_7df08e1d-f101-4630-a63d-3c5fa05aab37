// src/auth/auth.service.ts
import { Inject, Injectable, Logger, Scope, HttpException, HttpStatus } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request, Response } from 'express';
import { AuthenticatedUser, AuthProvider } from './dto/authenticated-user.dto.js';
import { RedisService } from '../redis/redis.service.js';
import { GithubAuthStrategy } from './strategies/github-auth.strategy.js';
import { LinuxdoAuthStrategy } from './strategies/linuxdo-auth.strategy.js';
import { IAuthStrategy } from './strategies/auth.strategy.interface.js';
import { ConfigService } from '@nestjs/config'; // Added ConfigService
import { URL } from 'url'; // Added URL import

@Injectable({ scope: Scope.REQUEST })
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private strategies: Map<AuthProvider, IAuthStrategy>;
  private readonly OAUTH_FALLBACK_REDIRECT_URL: string;

  constructor(
    private readonly configService: ConfigService, // Injected ConfigService
    private readonly githubStrategy: GithubAuthStrategy,
    private readonly linuxdoStrategy: LinuxdoAuthStrategy,
    private readonly redisService: RedisService, // Keep for caching user info
    @Inject(REQUEST) private readonly request: Request,
  ) {
    this.logger.log('AuthService initialized with strategies.');
    this.strategies = new Map<AuthProvider, IAuthStrategy>();
    this.strategies.set('github', this.githubStrategy);
    this.strategies.set('linuxdo', this.linuxdoStrategy);
    this.OAUTH_FALLBACK_REDIRECT_URL = this.configService.getOrThrow<string>('OAUTH_FALLBACK_REDIRECT_URL');

  }

  getLoginUrl(provider: AuthProvider, state?: string): string {
    const strategy = this.strategies.get(provider);
    if (!strategy) {
      throw new HttpException(`Unsupported auth provider: ${provider}`, HttpStatus.BAD_REQUEST);
    }
    const authUrl = strategy.getAuthUrl(state);
    this.logger.debug(`Generated login URL for ${provider} via strategy: ${authUrl}`);
    return authUrl;
  }

  private isValidRedirectUrl(url: string): boolean {
    // Removed try-catch. If url is invalid, new URL() will throw.
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol);
  }

  async handleProviderCallback(
    provider: AuthProvider,
    code: string,
    expressResponse: Response, // Express Response object for redirecting
    state?: string,
  ): Promise<void> { // Returns void as it will handle the redirect
    this.logger.debug(`AuthService: handleProviderCallback for ${provider} with code: ${code}, state: ${state}`);
    const strategy = this.strategies.get(provider);

    if (!strategy) {
      this.logger.error(`Unsupported auth provider: ${provider} in handleProviderCallback`);
      throw new HttpException(`Unsupported auth provider: ${provider}`, HttpStatus.BAD_REQUEST);
    }

    // Handle the callback and get user info
    const user = await strategy.handleCallback(code, expressResponse, state);

    // Get the access token from the user object returned by the strategy
    const accessToken = user.providerAccessToken;
    if (accessToken) {
      await this.cacheUserInfo(provider, accessToken, user);
    } else {
      this.logger.warn(`User object returned from ${provider} handleCallback is missing providerAccessToken.`);
    }

    // Handle redirect after successful authentication
    let targetRedirect = this.OAUTH_FALLBACK_REDIRECT_URL;
    if (state && this.isValidRedirectUrl(state)) {
      this.logger.log(`Redirecting to state URL: ${state}`);
      targetRedirect = state;
    } else if (state) {
      this.logger.warn(`Invalid or disallowed state URL provided for ${provider} callback: ${state}. Redirecting to default success URL.`);
    }

    if (!expressResponse.headersSent) {
      expressResponse.redirect(targetRedirect);
    } else {
      this.logger.warn(`Headers already sent for ${provider} callback, cannot redirect to ${targetRedirect}. User should be authenticated.`);
    }
  }

  private readonly USER_INFO_CACHE_PREFIX = 'user_info_cache_v2:';
  private readonly USER_INFO_CACHE_TTL = 3600; // 1 hour

  /**
   * Cache user information in Redis
   * @param provider The authentication provider
   * @param accessToken The access token
   * @param user The authenticated user object
   */
  private async cacheUserInfo(provider: AuthProvider, accessToken: string, user: AuthenticatedUser): Promise<void> {
    if (!accessToken || !user) {
      this.logger.warn(`Cannot cache user info: missing access token or user object`);
      return;
    }

    const cacheKey = `${this.USER_INFO_CACHE_PREFIX}${provider}:${accessToken}`;
    // Removed try-catch, Redis errors will propagate up
    await this.redisService.set(cacheKey, JSON.stringify(user), this.USER_INFO_CACHE_TTL);
    this.logger.debug(`User info cached for ${provider} under key ${cacheKey}`);
  }

  /**
   * Get cached user information from Redis.
   * Returns undefined if the user is not found in cache, accessToken is invalid, or if parsing fails.
   * Redis operational errors will propagate.
   * @param provider The authentication provider
   * @param accessToken The access token
   * @returns The cached user object or undefined.
   */
  private async getCachedUserInfo(provider: AuthProvider, accessToken: string): Promise<AuthenticatedUser | undefined> {
    if (!accessToken) {
      this.logger.debug(`getCachedUserInfo: No access token provided for ${provider}.`);
      return undefined;
    }

    const cacheKey = `${this.USER_INFO_CACHE_PREFIX}${provider}:${accessToken}`;
    const cachedUserString = await this.redisService.get(cacheKey); // Redis errors will propagate

    if (cachedUserString) {
      this.logger.debug(`User info cache hit for ${provider} with key ${cacheKey}.`);
      // Removed try-catch for JSON.parse. If data is corrupted, error will propagate.
      // This is consistent with "fail fast" for corrupted data.
      // A more robust solution might involve marking the cache entry as invalid or attempting to repair.
      // For now, letting it throw is the simplest "fail fast" approach.
      return JSON.parse(cachedUserString) as AuthenticatedUser;
    } else {
      this.logger.debug(`User info cache miss for ${provider} with key ${cacheKey}.`);
      return undefined;
    }
  }

  async getCurrentAuthenticatedUser(): Promise<AuthenticatedUser|null> {
    const providersToCheck: AuthProvider[] = ['github', 'linuxdo']; // Order might matter if user can be logged into both

    for (const provider of providersToCheck) {
      const strategy = this.strategies.get(provider);
      if (!strategy) {
        this.logger.debug(`Strategy not found for provider: ${provider}. Skipping.`);
        continue;
      }

      const accessTokenCookieName = provider === 'github' ? 'gh_access_token' : 'ld_access_token';
      const refreshTokenCookieName = provider === 'github' ? 'gh_refresh_token' : 'ld_refresh_token';

      let accessToken = this.request.signedCookies?.[accessTokenCookieName];
      const refreshTokenValue = this.request.signedCookies?.[refreshTokenCookieName];

      // Try to authenticate with access token
      if (accessToken) {
        this.logger.debug(`Access token found for provider: ${provider}. Verifying...`);

        // Try to get user from cache first
        const cachedUser = await this.getCachedUserInfo(provider, accessToken);
        if (cachedUser) {
          this.logger.debug(`User ${cachedUser.username} (${provider}) found in cache.`);
          return cachedUser;
        }
        this.logger.debug(`User not found in cache for ${provider}. Trying API.`);

        // If not in cache, try to get user info from provider
        // Any error from strategy.getUserInfo or cacheUserInfo will propagate up.
        const userFromApi = await strategy.getUserInfo(accessToken);
        if (userFromApi) {
          await this.cacheUserInfo(provider, accessToken, userFromApi); // Error here will propagate
          this.logger.log(`Authenticated user ${userFromApi.username} via ${userFromApi.provider} (current token).`);
          return userFromApi;
        }
        // If userFromApi is null/undefined (strategy might return that on non-error cases like 'user not found by token')
        // we fall through to refresh token logic.
        this.logger.warn(`User not found via current ${provider} access token. Attempting refresh.`);
      }

      // Try to refresh token if available
      if (refreshTokenValue && strategy.refreshToken) {
        this.logger.debug(`Attempting to refresh token for provider: ${provider}.`);
        const expressResponse = this.request.res as Response;
        if (!expressResponse) {
          // This is a programming error or unexpected state, let it be noticeable.
          // Or, if this is a recoverable scenario, a specific error could be thrown.
          // For now, logging and continuing to next provider if any.
          this.logger.error(`Cannot refresh token for ${provider}: Express response object not available.`);
          continue;
        }

        // Any error from strategy.refreshToken or cacheUserInfo will propagate up.
        const userFromRefreshToken = await strategy.refreshToken(refreshTokenValue, expressResponse);
        if (userFromRefreshToken) {
          const newAccessToken = userFromRefreshToken.providerAccessToken;
          if (newAccessToken) {
            await this.cacheUserInfo(provider, newAccessToken, userFromRefreshToken); // Error here will propagate
            this.logger.log(`Authenticated user ${userFromRefreshToken.username} via ${userFromRefreshToken.provider} (refreshed token).`);
            return userFromRefreshToken;
          } else {
            this.logger.error(`User object returned from ${provider} refreshToken is missing providerAccessToken.`);
            // This is a problematic state from the strategy, but we might try next provider.
            // Or throw an error immediately. For now, continue to check other providers.
          }
        } else {
          this.logger.warn(`Token refresh failed for provider ${provider} (no user returned).`);
        }
      } else if (refreshTokenValue && !strategy.refreshToken) {
        this.logger.warn(`Refresh token found for ${provider}, but strategy does not implement refreshToken method.`);
      }
    } // end for loop

    this.logger.warn('No authenticated user found after checking all providers.');
    return null;
  }

  async logout(expressResponse: Response): Promise<void> {
    this.logger.debug('AuthService: logout called, clearing cookies for all providers via strategies.');
    this.strategies.forEach((strategy, providerName) => {
      this.logger.log(`Clearing cookies for provider: ${providerName}`);
      strategy.clearCookies(expressResponse);
    });
  }


}
