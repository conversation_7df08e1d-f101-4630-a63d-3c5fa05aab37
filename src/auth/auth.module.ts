// src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthController } from './auth.controller.js';
import { AuthService } from './auth.service.js';
import { RedisModule } from '../redis/redis.module.js';
import { GithubAuthStrategy } from './strategies/github-auth.strategy.js';
import { LinuxdoAuthStrategy } from './strategies/linuxdo-auth.strategy.js';
import { HttpModule } from '../common/http.module.js';

@Module({
  imports: [
    HttpModule,    // Needed by strategies if they make HTTP calls directly
    ConfigModule,  // Needed by strategies for configuration
    RedisModule,   // Needed by AuthService for caching
  ],
  controllers: [AuthController],
  providers: [
    AuthService, 
    GithubAuthStrategy,
    LinuxdoAuthStrategy,
  ],
  exports: [AuthService],
})
export class AuthModule {}