// src/auth/auth.controller.ts
import { <PERSON>, <PERSON>, Lo<PERSON>, Query, <PERSON><PERSON>, Req, HttpException, HttpStatus, UseGuards, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service.js';
import { Response, Request } from 'express';
import { ConfigService } from '@nestjs/config'; // Potentially remove if no longer needed here
import { AuthenticatedUser } from './dto/authenticated-user.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js'; // CORRECTED PATH
import { CurrentUser } from './decorators/current-user.decorator.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js'; // CORRECTED PATH
import { PermissionLevel } from '../common/decorators/auth.enum.js'; // CORRECTED PATH


@Controller('/auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService, // Keep for logout redirect URL
  ) {}

  // ... (githubLoginRedirect, linuxdoLoginRedirect methods remain the same) ...
  @Get('/github/login')
  githubLoginRedirect(@Query('state') state: string, @Res() res: Response) {
    this.logger.log(`GitHub login initiated by user with state: ${state}`);
    const authUrl = this.authService.getLoginUrl('github', state);
    res.redirect(authUrl);
  }

  @Get('/github/callback')
  async githubCallback(@Query('code') code: string, @Query('state') state: string, @Res() res: Response): Promise<void> {
    this.logger.log(`GitHub callback received by controller. Code: ${code ? 'present' : 'missing'}, State: ${state}`);
    if (!code) {
      this.logger.error('GitHub callback: Authorization code is missing in controller.');
      // AuthService will handle redirecting to an error page if code is missing when it re-checks.
      // Or, throw here to be caught by global exception filter, which then calls MonitorService.
      // For consistency, let AuthService handle the user-facing redirect.
      // However, a basic check here is fine.
      throw new HttpException('Authorization code is missing from GitHub callback.', HttpStatus.BAD_REQUEST);
    }
    // No try-catch here, AuthService.handleProviderCallback will manage the response/redirect.
    await this.authService.handleProviderCallback('github', code, res, state);
  }

  @Get('/linuxdo/login')
  linuxdoLoginRedirect(@Query('state') state: string, @Res() res: Response) {
    this.logger.log(`Linux.do login initiated by user with state: ${state}`);
    const authUrl = this.authService.getLoginUrl('linuxdo', state);
    res.redirect(authUrl);
  }

  @Get('/linuxdo/callback')
  async linuxdoCallback(@Query('code') code: string, @Query('state') state: string, @Res() res: Response): Promise<void> {
    this.logger.log(`Linux.do callback received by controller. Code: ${code ? 'present' : 'missing'}, State: ${state}`);
    if (!code) {
      this.logger.error('Linux.do callback: Authorization code is missing in controller.');
      throw new HttpException('Authorization code is missing from Linux.do callback.', HttpStatus.BAD_REQUEST);
    }
    // No try-catch here, AuthService.handleProviderCallback will manage the response/redirect.
    await this.authService.handleProviderCallback('linuxdo', code, res, state);
  }

  @Get('/logout')
  async logout(@Res() res: Response) {
    this.logger.log('Logout initiated by user.');
    await this.authService.logout(res);
    const targetRedirect = this.configService.get<string>('FRONTEND_LOGOUT_REDIRECT_URL', '/');
    res.redirect(targetRedirect);
  }
  
  @Get('/me') // Renamed from getAuthStatus for clarity, aligns with common practice
  @UseGuards(AuthGuard) // Use the common AuthGuard
  @AuthPermissions(PermissionLevel.AUTH_USER) // Explicitly state that this requires an authenticated user
  async getMe(@CurrentUser() user: AuthenticatedUser): Promise<AuthenticatedUser> {
    this.logger.log(`Auth /me endpoint called for user: ${user?.username} via ${user?.provider}`);
    if (!user) {
      // This case should ideally be caught by AuthGuard if AUTH_USER is the default or specified
      throw new UnauthorizedException('User not found after guard execution.');
    }
    return user;
  }
  // ... (logout, getMe methods remain the same) ...
}