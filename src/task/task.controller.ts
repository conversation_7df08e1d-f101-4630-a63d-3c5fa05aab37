import { TaskLogicList, TaskLogicItem, TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  UsePipes,
  ValidationPipe,
  UseGuards
} from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto.js';
import { UpdateTaskDto } from './dto/update-task.dto.js';
import { ExecuteTaskLogicDto } from './dto/execute-task-logic.dto.js';
import { TaskService, TaskData, TaskLogEntry } from './task.service.js'; // Import TaskData and TaskLogEntry
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';
import { TaskExecutionService } from '../common/services/task-execution.service.js';


@Controller('/task') 
export class TaskController {
  constructor(
    private readonly taskService: TaskService,
    private readonly taskExecutionService: TaskExecutionService
  ) {}

  @Post()
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async create(@Body() createTaskDto: CreateTaskDto): Promise<TaskData> {
    return this.taskService.create(createTaskDto);
  }

  @Get()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async findAll(): Promise<TaskData[]> {
    return this.taskService.findAll();
  }

  @Get('/logic/list')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async getTaskLogicList(): Promise<TaskLogicItem[]> {
    return TaskLogicList;
  }

  @Get('/:id')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async findOne(@Param('id') id: string): Promise<TaskData> {
    return this.taskService.findOne(id);
  }

  @Put('/:id')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto): Promise<TaskData> {
    return this.taskService.update(id, updateTaskDto);
  }

  @Delete('/:id')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async remove(@Param('id') id: string): Promise<boolean> {
    return this.taskService.remove(id);
  }

  @Get('/:id/logs')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async findLogs(@Param('id') id: string): Promise<TaskLogEntry[]> {
    return this.taskService.findLogs(id);
  }

  
  @Post('/execute-logic')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async executeTaskLogic(@Body() executedTask: ExecuteTaskLogicDto): Promise<any> {
    // 调用 TaskService 中的方法执行任务逻辑
    return this.taskService.executeTaskLogic(executedTask);
  }
}
