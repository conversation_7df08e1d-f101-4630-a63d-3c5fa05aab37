import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
// import { DiscoveryModule } from '@golevelup/nestjs-discovery'; // No longer needed here as TaskExecutionService handles discovery
import { TaskService } from './task.service.js';
import { TaskController } from './task.controller.js';
import { RedisModule } from '../redis/redis.module.js';
import { AuthModule } from '../auth/auth.module.js';
import { CommonModule } from '../common/common.module.js';
// TaskLoggerService 已经被移除，日志功能已整合到 GlobalLoggerService 中

@Module({
  imports: [
    RedisModule,
    ScheduleModule.forRoot(),
    AuthModule,
    CommonModule // 显式导入 CommonModule 确保依赖关系清晰
  ],
  controllers: [TaskController],
  providers: [TaskService], // 移除 TaskLoggerService
  exports: [], // 不再需要导出任何服务
})
export class TaskModule {}
