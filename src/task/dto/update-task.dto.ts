import { PartialType } from '@nestjs/mapped-types';
import { CreateTaskDto } from './create-task.dto.js';
import { IsEnum, IsOptional } from 'class-validator';
import { TaskLogicEnum } from '../../common/enums/task-logic.enum.js'; // esm project, import local ts source code should using .js suffix

export class UpdateTaskDto extends PartialType(CreateTaskDto) {
  @IsOptional()
  @IsEnum(TaskLogicEnum)
  logicId?: TaskLogicEnum;
}