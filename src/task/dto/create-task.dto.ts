import { IsNotEmpty, IsString, IsOptional, IsObject, IsBoolean, IsEnum } from 'class-validator';
import { TaskLogicEnum } from '../../common/enums/task-logic.enum.js'; // esm project, import local ts source code should using .js suffix

export class CreateTaskDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  cron: string;

  @IsNotEmpty()
  @IsEnum(TaskLogicEnum)
  logicId: TaskLogicEnum;

  @IsOptional()
  @IsObject()
  params?: Record<string, any>;

  @IsOptional()
  @IsBoolean()
  isEnabled?: boolean;
}