import { Injectable, Logger, NotFoundException, OnModuleInit, ConflictException, HttpException, HttpStatus } from '@nestjs/common';
import { TaskLoggerService } from '../common/services/task-logger.service.js';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob, validateCronExpression } from 'cron';
import { CreateTaskDto } from './dto/create-task.dto.js';
import { UpdateTaskDto } from './dto/update-task.dto.js';
import { RedisService } from '../redis/redis.service.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { TaskLogStatus } from '../common/enums/task-log-status.enum.js';
import { TaskExecutionService } from '../common/services/task-execution.service.js';
import { ExecuteTaskLogicDto } from './dto/execute-task-logic.dto.js';

// src/core/async-context.store.ts
import { taskDataContext } from '../common/context/async-context.store.js';


export interface TaskLogMessage {
  timestamp: string; // Asia/Shanghai formatted string for the specific message
  message: string;
}

export interface TaskLogEntry {
  timestamp: string; // Asia/Shanghai formatted string - "触发时间" (trigger time of this log entry/status change)
  status: TaskLogStatus;
  messages: TaskLogMessage[]; // Array of messages related to this entry, newest first
  durationMs?: number; // Optional: execution time in milliseconds
}

export interface TaskData { // 导出 TaskData 接口
  name: string;
  cron: string;
  logicId: TaskLogicEnum; // 更新为 TaskLogicEnum
  params?: Record<string, any>;
  logs: TaskLogEntry[]; // Changed from string[]
  isEnabled?: boolean; // 添加 isEnabled 字段用于控制任务启用状态
  executionCount: number; // Added execution count
}

@Injectable()
export class TaskService implements OnModuleInit {
  private readonly logger = new Logger(TaskService.name);
  private readonly SCHEDULE_TASKS_KEY = 'schedule:tasks';
  private readonly SCHEDULE_TASKS_NAME_LIST_KEY = 'schedule:tasks:names';

  constructor(
    private readonly redisService: RedisService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly taskLoggerService: TaskLoggerService, 
    private readonly taskExecutionService: TaskExecutionService,
  ) {}

  async onModuleInit() {
    // Task executor discovery is now handled by TaskExecutionService's onModuleInit.
    // TaskExecutionService should be a global module or imported into TaskModule
    // so its onModuleInit runs.

    this.logger.log('Initializing tasks from Redis in TaskService...');
    
    // 验证排序列表与哈希表的一致性
    await this.validateTaskNameList();
    
    const tasks = await this.findAll();
    for (const task of tasks) {
      this.logger.log(`Initializing task from Redis: ${JSON.stringify(task)}`);
      if (task.isEnabled !== false) {
        this.addCronJob(task);
      }
    }
    this.logger.log(`Initialized ${tasks.length} tasks from Redis.`);
  }
  
  /**
   * 验证任务名称列表和哈希表的一致性
   * 如果名称列表不存在或不一致，则重新创建
   */
  private async validateTaskNameList(): Promise<void> {
    const allTasksData = await this.redisService.hgetall(this.SCHEDULE_TASKS_KEY);
    const taskNames = Object.keys(allTasksData || {});
    
    const existingNameList = await this.redisService.lrange(this.SCHEDULE_TASKS_NAME_LIST_KEY, 0, -1);
    const needsRebuild = !existingNameList ||
                         existingNameList.length !== taskNames.length ||
                         !taskNames.every(name => existingNameList.includes(name));
    
    if (needsRebuild && taskNames.length > 0) {
      this.logger.log('Rebuilding task name list for sorting consistency');
      // 删除现有列表
      if (existingNameList && existingNameList.length > 0) {
        await this.redisService.del(this.SCHEDULE_TASKS_NAME_LIST_KEY);
      }
      
      // 重新创建列表
      if (taskNames.length > 0) {
        await this.redisService.rpush(this.SCHEDULE_TASKS_NAME_LIST_KEY, taskNames);
      }
      
      this.logger.log(`Task name list rebuilt with ${taskNames.length} entries`);
    }
  }


  private validateCron(expression: string) {
    const validation = validateCronExpression(expression);
    this.logger.log(`Is the cron expression valid? ${validation.valid}`);
    if (!validation.valid) {
      throw new HttpException(validation.error?.message || "表达式错误", HttpStatus.BAD_REQUEST)
    }
  }
  

  private addCronJob(task: TaskData) {

    const job = new CronJob(task.cron, async () => {
      this.logger.log(`Cron job triggered for task: ${task.name} with logicId: ${task.logicId}`);
      // Logging is now handled within _executeTaskLogic
      await this._executeTaskLogic(task.name);
    });

    try {
      this.schedulerRegistry.addCronJob(task.name, job);
      job.start();
      this.logger.log(`Task ${task.name} added to scheduler with cron: ${task.cron}`);
    } catch (error) {
      this.logger.error(`Error adding cron job ${task.name}: ${error.message}`);
    }
  }

  private deleteCronJob(taskName: string) {
    try {
      if (this.schedulerRegistry.doesExist('cron', taskName)) {
         this.schedulerRegistry.deleteCronJob(taskName);
         this.logger.log(`Task ${taskName} deleted from scheduler.`);
      } else {
        this.logger.warn(`Cron job ${taskName} not found in scheduler, skipping deletion.`);
      }
    } catch (error) {
      // NestJS SchedulerRegistry.deleteCronJob 可能会在任务不存在时抛出错误
      // 根据 @nestjs/schedule 的实现，它内部会检查是否存在，所以理论上不应该在这里捕获 "not found" 错误
      // 但为了保险起见，可以添加日志
      this.logger.error(`Error deleting cron job ${taskName}: ${error.message}`);
    }
  }


  async create(createTaskDto: CreateTaskDto): Promise<TaskData> {
    this.logger.log(`Creating task with DTO: ${JSON.stringify(createTaskDto)}`); // 添加日志
    const existingByName = await this.redisService.hget(this.SCHEDULE_TASKS_KEY, createTaskDto.name);
    if (existingByName) {
      throw new ConflictException(`Task with name "${createTaskDto.name}" already exists.`);
    }

    const task: TaskData = {
      ...createTaskDto,
      logs: [], // Initialize logs as an empty array of TaskLogEntry
      isEnabled: createTaskDto.isEnabled === undefined ? true : createTaskDto.isEnabled, // Default to true
      executionCount: 0, // Initialize executionCount
    };

    this.validateCron(task.cron);

    await this.redisService.hset( // 使用公共方法
      this.SCHEDULE_TASKS_KEY,
      task.name, // task.name is the ID here
      JSON.stringify(task),
    );
    
    // 将任务名称添加到排序列表中
    await this.redisService.rpush(this.SCHEDULE_TASKS_NAME_LIST_KEY, task.name);
    if (task.isEnabled) {
      this.addCronJob(task);
    }
    return task;
  }

  async findAll(): Promise<TaskData[]> {
    // 1. 首先获取哈希表中的所有任务数据
    const tasks = await this.redisService.hgetall(this.SCHEDULE_TASKS_KEY);
    if (!tasks || Object.keys(tasks).length === 0) {
      return [];
    }

    // 2. 获取排序列表
    const taskNames = await this.redisService.lrange(this.SCHEDULE_TASKS_NAME_LIST_KEY, 0, -1);
    
    // 3. 准备返回的任务数据数组
    let tasksList: TaskData[] = [];
    
    // 4. 如果排序列表存在且不为空，使用它来排序
    if (taskNames && taskNames.length > 0) {
      const processedNames = new Set<string>();
      
      // 按列表顺序添加任务
      for (const name of taskNames) {
        const taskString = tasks[name];
        if (taskString) {
          const task = JSON.parse(taskString as string) as TaskData;
          // 移除 logs 字段以减小数据量
          if (task && typeof task === 'object' && 'logs' in task) {
            delete (task as any).logs;
          }
          tasksList.push(task);
          processedNames.add(name);
        }
      }
      
      // 添加哈希表中存在但不在列表中的任务
      for (const name in tasks) {
        if (!processedNames.has(name)) {
          const task = JSON.parse(tasks[name] as string) as TaskData;
          // 移除 logs 字段
          if (task && typeof task === 'object' && 'logs' in task) {
            delete (task as any).logs;
          }
          tasksList.push(task);
        }
      }
    } else {
      // 5. 如果排序列表不存在或为空，直接返回所有任务数据
      tasksList = Object.values(tasks).map((taskString: string) => {
        const task = JSON.parse(taskString as string) as TaskData;
        // 移除 logs 字段
        if (task && typeof task === 'object' && 'logs' in task) {
          delete (task as any).logs;
        }
        return task;
      });
    }
    
    return tasksList;
  }

  async findOne(id: string): Promise<TaskData> {
    const taskString = await this.redisService.hget( // 使用公共方法
      this.SCHEDULE_TASKS_KEY,
      id,
    );
    if (!taskString) {
      throw new NotFoundException(`Task with ID "${id}" not found`);
    }
    return JSON.parse(taskString);
  }

  async update(id: string, updateTaskDto: UpdateTaskDto): Promise<TaskData> {
    const existingTask = await this.findOne(id); // 确保任务存在, id is the current name/ID of the task
    const oldCronTime = existingTask.cron;
    const oldIsEnabled = existingTask.isEnabled;

    // Check for name conflict if name is being changed
    // id is the current key in Redis (current task name)
    // updateTaskDto.name is the proposed new value for the 'name' property within the task data
    if (updateTaskDto.name && updateTaskDto.name !== id) {
      const conflictingTask = await this.redisService.hget(this.SCHEDULE_TASKS_KEY, updateTaskDto.name);
      // If a task already exists with the proposed new name as its key/ID
      if (conflictingTask) {
        throw new ConflictException(
          `Cannot update task's name property to "${updateTaskDto.name}" as another task with that name/ID already exists.`,
        );
      }
    }

    const updatedTask: TaskData = { // 明确 updatedTask 类型
      ...existingTask,
      ...updateTaskDto, // This will overwrite existingTask.name if updateTaskDto.name is present
    };

    this.validateCron(updatedTask.cron);

    // The key in Redis (id) does not change here. We are updating the content of the task identified by 'id'.
    await this.redisService.hset( // 使用公共方法
      this.SCHEDULE_TASKS_KEY,
      id, // id is the original name/key, it's not changed by this DTO directly
      JSON.stringify(updatedTask),
    );

    // 检查 cron 表达式或启用状态是否变化
    const cronChanged = updateTaskDto.cron && updateTaskDto.cron !== oldCronTime;
    const enabledChanged = updateTaskDto.isEnabled !== undefined && updateTaskDto.isEnabled !== oldIsEnabled;

    if (cronChanged || enabledChanged) {
      this.deleteCronJob(id); // 删除旧任务
      if (updatedTask.isEnabled) { // 如果新状态是启用，则添加新任务
        this.addCronJob(updatedTask);
      }
    } else if (updatedTask.isEnabled && !this.schedulerRegistry.doesExist('cron', id)) {
      // 如果任务是启用的，但调度器中不存在（可能因为之前被禁用或创建时未启用），则添加
      this.addCronJob(updatedTask);
    }


    return updatedTask;
  }

  async remove(id: string): Promise<boolean> {
    await this.findOne(id); // 确保任务存在，否则 findOne 会抛出 NotFoundException
    this.deleteCronJob(id);
    const result = await this.redisService.hdel(this.SCHEDULE_TASKS_KEY, id); // 使用公共方法
    
    // 从排序列表中删除任务名称
    await this.redisService.lrem(this.SCHEDULE_TASKS_NAME_LIST_KEY, 0, id); // count = 0 删除所有匹配项
    
    // hdel 返回删除的字段数量，如果任务存在且被删除，应为 1
    if (result === 0) {
      // 理论上，如果上面 findOne 成功，这里不应该为0，除非在极短时间内任务被其他进程删除
      this.logger.warn(`Task with ID "${id}" was found but hdel returned 0.`);
      // 可以选择不抛出错误，因为 cron job 已经被尝试删除
    }
    return true;
  }


  /**
   * 保存最新日志
   * @param task 
   * @param currentLogEntry 
   */
  async saveTaskLogs(task: TaskData, currentLogEntry: TaskLogEntry): Promise<void> {
      
      await this.redisService.hset(this.SCHEDULE_TASKS_KEY, task.name, JSON.stringify(task));
  }

   /**
   * 更新最新日志
   * @param task 
   * @param currentLogEntry 
   */
  async updateTaskLogs(task: TaskData, currentLogEntry: TaskLogEntry): Promise<void> {
      if (!task.logs) {
        task.logs = [ currentLogEntry ];
      }
      task.logs[0] = currentLogEntry; // Add new entry to the beginning

      await this.redisService.hset(this.SCHEDULE_TASKS_KEY, task.name, JSON.stringify(task));
  }



  /**
   * 
   * @param taskName 
   * @returns 
   */
  private async _executeTaskLogic(taskName: string): Promise<void> {

    const taskData = await this.findOne(taskName);

    await taskDataContext.run(taskData, async () => {
      try {
        //1. 更新任务状态 - started
        this.taskLoggerService.withBeginLog();
        await this.redisService.hset(this.SCHEDULE_TASKS_KEY, taskData.name, JSON.stringify(taskDataContext.getStore()));

        if (!this.taskExecutionService.isLogicRegistered(taskData.logicId)) {
          let errorMessage = `No executor found for logicId: ${taskData.logicId} (Task: ${taskName})`;
          this.logger.error(errorMessage); 
          throw new Error(errorMessage)
        }

        // 2. Execute the task logic using TaskExecutionService
        const executionResult = await this.taskExecutionService.executeLogic(
          taskData.logicId,
          taskData.params
        );

        this.taskLoggerService.withSuccessLog(executionResult)
        
      } catch(e) {
        this.taskLoggerService.withFailLog(e)
      } 

      await this.redisService.hset(this.SCHEDULE_TASKS_KEY, taskData.name, JSON.stringify(taskDataContext.getStore()));
      this.logger.log(`task-${taskData.name} executed`)
    })

  }

  /**
   * 
   * @param taskName 
   * @returns 
   */
  async findLogs(taskName: string): Promise<TaskLogEntry[]> {
    const task = await this.findOne(taskName);
    return task.logs || [];
  }

  /**
   * 执行指定的任务逻辑
   * @param name 要执行的任务名称
   * @param params 执行参数
   * @returns 执行结果
   */
  async executeTaskLogic(executedTask: ExecuteTaskLogicDto): Promise<any> {
      return this._executeTaskLogic(executedTask.name)
  }
}
