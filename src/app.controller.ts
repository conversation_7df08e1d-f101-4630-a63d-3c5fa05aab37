import { Controller, Get, Post, Body, Param, Query, HttpException, HttpStatus, UseGuards } from '@nestjs/common';
import { AppService } from './app.service.js';
import { AuthenticatedUser } from './auth/dto/authenticated-user.dto.js';
import { CurrentUser } from './auth/decorators/current-user.decorator.js';
import { AuthPermissions } from './common/decorators/auth.decorator.js';
import { AuthGuard } from './common/guards/auth.guard.js';
import { PermissionLevel } from './common/decorators/auth.enum.js';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @UseGuards(AuthGuard)
  getHello(@CurrentUser() user: AuthenticatedUser, @Query('error') error?: string): any {
    if (error === 'true') {
      throw new HttpException('This is a test error', HttpStatus.BAD_REQUEST);
    }

    return this.appService.getCachedData("12345");
  }

}
