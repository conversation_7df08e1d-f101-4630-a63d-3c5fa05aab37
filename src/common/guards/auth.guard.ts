// src/common/guards/auth.guard.ts
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../../auth/auth.service.js'; // Adjust path if necessary
import { AuthenticatedUser } from '../../auth/dto/authenticated-user.dto.js'; // Adjust path
import { PermissionLevel } from '../decorators/auth.enum.js';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    const requiredPermissions = this.reflector.getAllAndOverride<PermissionLevel[] | PermissionLevel>(
      'permissions', 
      [context.getHandler(), context.getClass()],
    );


    //  Handle API Key/Token based permissions (READ_ONLY, READ_WRITE)
    const querySecret = request.query.secret as string;
    const authorizationHeader = request.headers['authorization'] as string; // Lowercase 'authorization' is common

    const apiKeyReadOnly = this.configService.get<string>('API_KEY_READ_ONLY');
    const apiKeyReadWrite = `Bearer ${this.configService.get<string>('API_KEY_READ_WRITE')}`;

    // Check for READ_ONLY permission
    if (requiredPermissions === PermissionLevel.READ_ONLY ||
        (Array.isArray(requiredPermissions) && requiredPermissions.includes(PermissionLevel.READ_ONLY))) {
      if ((querySecret && querySecret === apiKeyReadOnly) || (authorizationHeader && authorizationHeader === apiKeyReadWrite)) {
        this.logger.debug('AuthGuard: READ_ONLY access granted via query secret or Authorization header (using READ_WRITE key).');
        return true;
      } else {
        this.logger.warn('AuthGuard: READ_ONLY permission required, but provided keys/tokens are invalid or missing.');
        throw new UnauthorizedException('Invalid or missing API key/token for READ_ONLY access.');
      }
    }
    
    // Check for READ_WRITE permission (only if not already granted by READ_ONLY logic that might use READ_WRITE key)
    if (requiredPermissions === PermissionLevel.READ_WRITE ||
        (Array.isArray(requiredPermissions) && requiredPermissions.includes(PermissionLevel.READ_WRITE))) {
      if (authorizationHeader && authorizationHeader === apiKeyReadWrite) {
        this.logger.debug('AuthGuard: READ_WRITE access granted via Authorization header.');
        return true;
      } else {
        this.logger.warn('AuthGuard: READ_WRITE permission required, but Authorization header is invalid or missing.');
        throw new UnauthorizedException('Invalid or missing API token for READ_WRITE access.');
      }
    }


    const user: AuthenticatedUser | null = await this.authService.getCurrentAuthenticatedUser();

    if (user) {
      request.user = user;
      this.logger.debug(`AuthGuard: User ${user.username} authenticated via session for requirement.`);
      return true;
    }

    if (requiredPermissions === PermissionLevel.PUBLIC ||
      (Array.isArray(requiredPermissions) && requiredPermissions.includes(PermissionLevel.PUBLIC))) {
        this.logger.debug('AuthGuard: PUBLIC access without user allowed.');
        return true;
    }
    else {
        // Handle AUTH_USER or default (which implies AUTH_USER)
        this.logger.warn('AuthGuard: AUTH_USER session required, but no authenticated user found.');
        throw new UnauthorizedException('User is not authenticated.');
    }

  }
}
