import { Global, Module } from '@nestjs/common';
import { TaskExecutionService } from './services/task-execution.service.js';
import { TaskLoggerService } from './services/task-logger.service.js';
import { DiscoveryModule } from '@golevelup/nestjs-discovery';
import { RedisModule } from '../redis/redis.module.js';

@Global() // Making it global so services are available everywhere
@Module({
  imports: [DiscoveryModule, RedisModule],
  providers: [
    TaskExecutionService,
    TaskLoggerService,
  ],
  exports: [
    TaskExecutionService,
    TaskLoggerService,
  ],
})
export class CommonModule {}