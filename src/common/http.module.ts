import { Modu<PERSON>, Logger } from '@nestjs/common';
import { HttpModule as AxiosHttpModule, HttpModuleOptions } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpsProxyAgent } from 'https-proxy-agent';

@Module({
  imports: [
    AxiosHttpModule.registerAsync({
      imports: [ConfigModule], // 确保 ConfigModule 可用，因为要注入 ConfigService
      useFactory: async (configService: ConfigService): Promise<HttpModuleOptions> => {
        const logger = new Logger('HttpModule'); // 更好的日志名，表明是代理模块的配置
        const proxyUrl = configService.get<string>('HTTP_PROXY');
        let agentConfig = {};

        if (proxyUrl) {
          try {
            agentConfig = {
              httpsAgent: new HttpsProxyAgent(proxyUrl),
            };
            logger.log(`HttpService configured to use HttpsProxyAgent with proxy: ${proxyUrl}`);
          } catch (e) {
            logger.error(`Invalid HTTP_PROXY for HttpsProxyAgent: ${proxyUrl}. Proxy agent will not be configured.`, e);
          }
        } else {
          logger.log('HTTP_PROXY not set. HttpService will not use HttpsProxyAgent.');
        }

        return {
          timeout: 30000,
          maxRedirects: 3,
          ...agentConfig,
        };
      },
      inject: [ConfigService],
    }),
  ],
  // 关键步骤：导出配置好的 HttpModule，以便其他模块可以导入它并使用其提供的 HttpService
  exports: [AxiosHttpModule],
})

export class HttpModule {}
