import { Injectable, Logger, OnModuleInit, NotFoundException } from '@nestjs/common';
import { DiscoveryService, DiscoveredMethodWithMeta } from '@golevelup/nestjs-discovery';
import { TASK_LOGIC_METADATA_KEY } from '../decorators/task-logic.decorator.js';
import { TaskLogicEnum } from '../enums/task-logic.enum.js';
import { TASK_PARAM_METADATA_KEY, TaskParamMapping } from '../decorators/task-param.decorator.js';

@Injectable()
export class TaskExecutionService implements OnModuleInit {
  private readonly logger = new Logger(TaskExecutionService.name);
  private taskExecutors = new Map<TaskLogicEnum, { instance: any; methodName: string }>();

  constructor(
    private readonly discoveryService: DiscoveryService,
  ) {}

  async onModuleInit() {
    this.logger.log('Discovering task executors for TaskExecutionService...');
    const allDiscoveredProviders = await this.discoveryService.providers(
      (provider) => provider.injectType !== undefined,
    );

    for (const discoveredProvider of allDiscoveredProviders) {
      const methodsWithMeta = await this.discoveryService.classMethodsWithMetaAtKey<TaskLogicEnum>(
        discoveredProvider,
        TASK_LOGIC_METADATA_KEY,
      );

      for (const discoveredMethodInfo of methodsWithMeta) {
        const logicId = discoveredMethodInfo.meta;
        const instance = discoveredProvider.instance;
        const methodName = discoveredMethodInfo.discoveredMethod.methodName;
        const className = discoveredProvider.name;

        if (this.taskExecutors.has(logicId)) {
          const existingExecutor = this.taskExecutors.get(logicId);
          const newLocation = `${className}.${methodName}`;
          const oldLocation = `${existingExecutor?.instance.constructor.name}.${existingExecutor?.methodName}`;
          this.logger.error(
            `Duplicate TaskLogicEnum found: ${logicId} on ${newLocation}. ` +
            `Previous one was on ${oldLocation}`,
          );
          // Consider re-throwing or a more robust error handling strategy if this service is critical path
          // For now, logging an error and continuing discovery, but the first registered executor will be used.
          // Or, to strictly follow original behavior:
          throw new Error(`Duplicate TaskLogicEnum: ${logicId}. Check ${newLocation} and ${oldLocation}`);
        }

        this.logger.log(`Discovered task executor for ${logicId}: ${className}.${methodName}`);
        this.taskExecutors.set(logicId, { instance, methodName });
      }
    }
    this.logger.log(`TaskExecutionService finished discovering ${this.taskExecutors.size} task executors.`);
  }

  isLogicRegistered(logicId: TaskLogicEnum): boolean {
    return this.taskExecutors.has(logicId);
  }

 

  async executeLogic(
    logicId: TaskLogicEnum,
    params: any
  ): Promise<any> {
    
    const executor = this.taskExecutors.get(logicId);

    if (!executor) {
      this.logger.error(`No executor found for logicId: ${logicId}`);
      throw new NotFoundException(`No executor found for logicId: ${logicId}`);
    }

    const { instance, methodName } = executor;
    const logMessage = `Attempting to execute task logic for ${logicId} via ${instance.constructor.name}.${methodName} with params: ${JSON.stringify(params)}`;
    this.logger.log(logMessage);

    try {
      const paramMappings: TaskParamMapping[] | undefined = Reflect.getOwnMetadata(
        TASK_PARAM_METADATA_KEY,
        instance.constructor,
        methodName,
      );

      let result;
      if (paramMappings && Array.isArray(paramMappings) && typeof params === 'object' && params !== null) {
        const args = paramMappings.map(mapping => (params as Record<string, any>)[mapping.paramKey]);
        result = await instance[methodName](...args);
      } else if (!paramMappings && (params === undefined || (typeof params === 'object' && params !== null && Object.keys(params).length === 0))) {
        result = await instance[methodName]();
      } else {
        // Fallback: pass params directly if no mapping or if params is not an object for mapping
        result = await instance[methodName](params);
      }

      // 记录执行成功的日志
      const successMsg = `Task logic executed successfully for ${logicId}`;
      this.logger.log(successMsg);

      return result;
    } catch (error) {
      const errorMsg = `Error executing task logic for ${logicId} via ${instance.constructor.name}.${methodName}: ${error.message}`;
      this.logger.error(errorMsg, error.stack);

      throw error; // Re-throw the error to be handled by the caller
    }
  }
}