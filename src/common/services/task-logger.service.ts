import { BadRequestException, ConsoleLogger, Injectable, Logger, LoggerService, LogLevel, Scope } from '@nestjs/common';
import { TaskData, TaskLogEntry, TaskLogMessage, TaskService } from '../../task/task.service.js';

import { TaskLogStatus } from '../enums/task-log-status.enum.js';
import { taskDataContext } from '../context/async-context.store.js'

/**
 * 全局日志服务
 * 拦截所有NestJS的日志输出，在有活跃任务上下文时自动记录到内存中
 * 整合了原TaskLoggerService的功能
 */
@Injectable()
export class TaskLoggerService extends ConsoleLogger implements LoggerService {


    /**
     * 初始化任务上下文
     * @returns 初始化的任务日志条目
     */
     withBeginLog() {

      const taskData = taskDataContext.getStore();

      if(!taskData) {
        throw new BadRequestException("current active context not exists")
      }
      
      // 初始化新的任务日志条目
      const currentLogEntry: TaskLogEntry = {
        timestamp: this.getFormattedTimestamp(), // "触发时间"
        status: TaskLogStatus.STARTED,
        messages: [
          {
            timestamp: this.getFormattedTimestamp(),
            message: `${taskData.name} Task execution initiated.`,
          }
        ],
      };


      if (!taskData.logs) {
        taskData.logs = [];
      }
      taskData.logs.unshift(currentLogEntry); // Add new entry to the beginning

      const MAX_LOGS = 100;
      if (taskData.logs.length > MAX_LOGS) {
        taskData.logs = taskData.logs.slice(0, MAX_LOGS);
      }
    
    }
  
    /**
     * 提交任务日志并标记为成功
     * @returns 任务日志条目
     */
    withSuccessLog(executeResult: any) {
      
      const taskData = taskDataContext.getStore();

      if(!taskData || !taskData.logs[0]) {
        throw new BadRequestException("current active context not exists")
      }
  
      const currentLogEntry = taskData.logs[0];
      
      // 设置任务状态为成功
      currentLogEntry.status = TaskLogStatus.SUCCESS;
      
      // 计算执行时间（毫秒）
      const startTime = new Date(currentLogEntry.timestamp).getTime();
      const endTime = new Date().getTime();
      currentLogEntry.durationMs = endTime - startTime;
  
      // 添加完成消息
      currentLogEntry.messages.push({
        timestamp: this.getFormattedTimestamp(),
        message: `Task completed successfully. Duration: ${currentLogEntry.durationMs}ms, Result: ${JSON.stringify(executeResult)}`,
      });

      taskData.executionCount = (taskData.executionCount || 0) + 1;
    }
  
    /**
     * 回滚任务并标记为失败
     * @param error 错误信息
     * @param taskName 可选的任务名称，如果不指定则回滚当前活跃上下文的任务
     * @returns 任务日志条目
     */
     withFailLog(error: Error | string) {
      const taskData = taskDataContext.getStore();
  
      if(!taskData || !taskData.logs[0]) {
        throw new BadRequestException("current active context not exists")
      }
  
      const currentLogEntry = taskData.logs[0];
    
      // 设置任务状态为失败
      currentLogEntry.status = TaskLogStatus.FAILURE;
      
      // 计算执行时间（毫秒）
      const startTime = new Date(currentLogEntry.timestamp).getTime();
      const endTime = new Date().getTime();
      currentLogEntry.durationMs = endTime - startTime;
      
      // 添加错误消息
      const errorMessage = typeof error === 'string' ? error : error.message || 'Unknown error';
      currentLogEntry.messages.push({
        timestamp: this.getFormattedTimestamp(),
        message: `Task failed: ${errorMessage}. Duration: ${currentLogEntry.durationMs}ms`,
      });
  
      // 如果是 Error 类型的对象，添加堆栈信息到日志中
      if (typeof error !== 'string' && error instanceof Error && error.stack) {
        currentLogEntry.messages.push({
          timestamp: this.getFormattedTimestamp(),
          message: `Error Stack: ${error.stack}`,
        });
      }
  
      taskData.executionCount = (taskData.executionCount || 0) + 1;
      
    }
  
  

  /**
   * 重写日志方法，添加拦截功能
   */
  override log(message: any, ...optionalParams: any[]) {
    super.log(message, ...optionalParams);
    this.captureLog('log', message, optionalParams);
  }

  override error(message: any, ...optionalParams: any[]) {
    super.error(message, ...optionalParams);
    this.captureLog('error', message, optionalParams);
  }

  override warn(message: any, ...optionalParams: any[]) {
    super.warn(message, ...optionalParams);
    this.captureLog('warn', message, optionalParams);
  }

  override debug(message: any, ...optionalParams: any[]) {
    super.debug(message, ...optionalParams);
    this.captureLog('debug', message, optionalParams);
  }

  override verbose(message: any, ...optionalParams: any[]) {
    super.verbose(message, ...optionalParams);
    this.captureLog('verbose', message, optionalParams);
  }

  /**
   * 捕获日志并将其添加到当前任务上下文
   */
  private captureLog(level: string, message: any, params: any[]): void {
    const taskData = taskDataContext.getStore();

    if(!taskData || !taskData.logs[0]) {
      return ;
    }

    const currentLogEntry = taskData.logs[0];
    
    // 格式化消息
    let formattedMessage = message;
    if (typeof message !== 'string') {
      try {
        formattedMessage = JSON.stringify(message);
      } catch (e) {
        formattedMessage = String(message);
      }
    }
    
    // 添加参数
    if (params && params.length > 0) {
      formattedMessage = `[${level}] ${params.map(p => {
        let str;
        if (typeof p === 'object') {
          try {
            str = JSON.stringify(p);
          } catch (e) {
            str = String(p);
          }
        } else {
          str = p;
        }
        return `[${str}]`;
      }).join(' ')} ${formattedMessage}`;
    }
    
    // 创建日志消息
    const timestamp = this.getFormattedTimestamp();
    const logMessage: TaskLogMessage = {
      timestamp,
      message: `${formattedMessage}`
    };
    
    // 添加到当前任务的pending日志
 
    // 同时更新当前日志条目的内存状态
    currentLogEntry.messages.push(logMessage);
  }

  /**
   * 获取格式化的时间戳
   */
  private getFormattedTimestamp(): string {
    const date = new Date();
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hourCycle: 'h23',
    };

    const formatter = new Intl.DateTimeFormat('en-US', options);
    const partsArray = formatter.formatToParts(date);

    const dateParts = partsArray.reduce((acc, part) => {
      if (part.type !== 'literal') {
        acc[part.type] = part.value;
      }
      return acc;
    }, {} as Record<string, string>);

    return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
  }
  
}