import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // Import Reflector
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IS_RAW_RESPONSE_KEY } from '../decorators/raw-response.decorator.js'; // Import the key

export interface Response<T> {
  status: number;
  message: string;
  data: T;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T> | T> { // Adjust return type
  constructor(private reflector: Reflector) {} // Inject Reflector

  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T> | T> { // Adjust return type
    const isRawResponse = this.reflector.getAllAndOverride<boolean>(IS_RAW_RESPONSE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isRawResponse) {
      // If RawResponse decorator is used, bypass transformation
      return next.handle();
    }

    const httpContext = context.switchToHttp();
    const request = httpContext.getRequest();
    const response = httpContext.getResponse();
    const httpStatus = response.statusCode;
    return next.handle().pipe(
      map(data => ({
        status: httpStatus,
        message: '接口调用成功', // 或者根据实际情况设置
        data,
      })),
    );
  }
}