// src/common/filters/http-exception.filter.ts
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { MonitorService } from '../../monitor/monitor.service.js';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly monitorService: MonitorService,
  ) {}

  async catch(exception: unknown, host: ArgumentsHost): Promise<void> {
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    const request = ctx.getRequest();
    // const response = ctx.getResponse(); // Direct response object

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    let errorMessage = 'Internal server error';
    let errorData: any = null; // Default data for errors

    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      if (typeof response === 'string') {
        errorMessage = response;
      } else if (typeof response === 'object' && response !== null) {
        errorMessage = (response as any).message || exception.message;
        // If the HttpException response contains more details, you might want to pass them in 'data'
        // For example, class-validator errors often come as an object with a 'message' array.
        if (Array.isArray((response as any).message) && (response as any).error) {
            errorData = { error: (response as any).error, details: (response as any).message };
            errorMessage = `${(response as any).error || 'Validation failed'}`; // A more generic message
        } else if ((response as any).error) {
             errorData = { error: (response as any).error };
        }
      }
    } else if (exception instanceof Error) {
        errorMessage = exception.message;
    }
    
    const errorName = exception instanceof Error ? exception.name : 'UnknownError';
    const stack = exception instanceof Error ? exception.stack : undefined;

    // Log the error locally and send to Telegram (existing logic)
    this.logger.error(
        `HTTP Error: ${httpStatus} - ${errorMessage} at ${httpAdapter.getRequestUrl(request)} (${httpAdapter.getRequestMethod(request)})`,
        stack,
        HttpExceptionFilter.name
    );
    if (!(exception instanceof HttpException) || httpStatus >= HttpStatus.INTERNAL_SERVER_ERROR) {
      let contextInfo = `Path: ${httpAdapter.getRequestUrl(request)}\nMethod: ${httpAdapter.getRequestMethod(request)}\nStatus: ${httpStatus}`;
      if (request.user) {
        contextInfo += `\nUser: ${JSON.stringify(request.user)}`;
      }
      const errorToSend = exception instanceof Error ? exception : new Error(String(exception));
      this.monitorService.sendErrorMessageToTelegram(errorToSend, contextInfo)
        .catch(teleError => {
            this.logger.error('HttpExceptionFilter: Failed to send error to Telegram from within filter itself.', teleError.stack);
        });
    }

    // Construct the new response body structure
    const responseBody = {
      status: httpStatus, // Use 'status' instead of 'statusCode'
      data: errorData,     // Use 'data' for additional error details or null
      message: errorMessage,
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, httpStatus);
  }
}