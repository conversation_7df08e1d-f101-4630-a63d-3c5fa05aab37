import { Logger } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service.js'; // Adjusted import path

export const REDIS_CACHE_KEY_METADATA = 'redis_cache_key_pattern';
export const REDIS_CACHE_TTL_METADATA = 'redis_cache_ttl';

export interface RedisCacheOptions {
  keyPattern: string;
  ttl?: number;
}

/**
 * Marks a service method to be cached using Redis.
 *
 * @param options - Configuration for Redis caching.
 * @param options.keyPattern - The pattern for the Redis cache key.
 *                             Placeholders like `{args[0]}` or `{args[0].id}` will be replaced
 *                             with actual argument values at runtime.
 * @param options.ttl - Optional. Time-to-live for the cache entry in seconds. Default is 3600 (1 hour).
 */
export function RedisCache(options: RedisCacheOptions): MethodDecorator {
  const { keyPattern, ttl } = options;

  return (target: any, propertyKey: string, descriptor: PropertyDescriptor): PropertyDescriptor => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const self = this;
      const loggerInstance = self.logger || self.loggerService || new Logger(target.constructor.name);
      const redisServiceInstance = self.redisService as RedisService;

      if (!redisServiceInstance) {
        loggerInstance.error(
          `[RedisCache] RedisService is not available in ${target.constructor.name}. Make sure 'redisService' is injected and assigned.`,
        );
        return originalMethod.apply(self, args);
      }

      let redisKey = keyPattern;
      try {
        // Replace placeholders like {args[0]} or {args[0].id}
        // More robust parsing might be needed for complex patterns
        redisKey = keyPattern.replace(/\{args\[(\d+)\](?:\.(\w+))?\}/g, (match, indexStr, prop) => {
          const index = parseInt(indexStr, 10);
          if (index < args.length) {
            const arg = args[index];
            if (prop && arg && typeof arg === 'object' && prop in arg) {
              return String(arg[prop]);
            }
            if (!prop && arg !== undefined) {
              return String(arg);
            }
          }
          loggerInstance.warn(`[RedisCache] Failed to substitute argument for placeholder: ${match} in key ${keyPattern}`);
          return match; // Return the original placeholder if substitution fails
        });
      } catch (e) {
        loggerInstance.error(`[RedisCache] Error generating cache key for ${target.constructor.name}.${propertyKey}: ${e.message}`, e.stack);
        return originalMethod.apply(self, args);
      }


      try {
        const cachedData = await redisServiceInstance.get(redisKey);
        if (cachedData) {
          loggerInstance.debug(`[RedisCache] Cache hit for key: ${redisKey} in ${target.constructor.name}.${propertyKey}`);
          try {
            return JSON.parse(cachedData);
          } catch (parseError) {
            loggerInstance.error(
              `[RedisCache] Failed to parse cached JSON for key ${redisKey}: ${parseError.message}. Fetching fresh data.`,
              parseError.stack,
            );
            // Proceed to fetch fresh data if parsing fails
          }
        }
        loggerInstance.debug(`[RedisCache] Cache miss for key: ${redisKey} in ${target.constructor.name}.${propertyKey}`);
      } catch (redisError) {
        loggerInstance.error(
          `[RedisCache] Redis GET operation failed for key ${redisKey}: ${redisError.message}. Proceeding with original method.`,
          redisError.stack,
        );
        // Proceed with original method if Redis GET fails
      }

      const result = await originalMethod.apply(self, args);

      if (result !== undefined && result !== null) {
        try {
          await redisServiceInstance.set(redisKey, JSON.stringify(result), ttl);
          loggerInstance.debug(`[RedisCache] Data cached for key: ${redisKey} with TTL: ${ttl}s in ${target.constructor.name}.${propertyKey}`);
        } catch (redisError) {
          loggerInstance.error(
            `[RedisCache] Redis SET operation failed for key ${redisKey}: ${redisError.message}`,
            redisError.stack,
          );
          // Do not re-throw, just log the error and return the result
        }
      }
      return result;
    };
    return descriptor;
  };
}