import { Logger } from '@nestjs/common';
import { LockService } from '../../redis/lock.service.js';

export class LockAcquisitionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'LockAcquisitionError';
  }
}

export interface RedisLockOptions {
  keyPattern: string; // e.g., 'user_operation:{args[0].id}'
  ttl?: number; // Lock TTL in seconds, defaults to 30
  onFailure?: 'throwError' | 'returnNull'; // Defaults to 'throwError'
}

export function RedisLock(options: RedisLockOptions): MethodDecorator {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor): PropertyDescriptor => {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const self = this;
      const loggerInstance = self.logger || self.loggerService || new Logger(target.constructor.name);
      const lockServiceInstance = self.lockService as LockService;

      if (!lockServiceInstance) {
        loggerInstance.error(
          `[RedisLock] LockService is not available in ${target.constructor.name}. Make sure 'lockService' is injected and assigned.`,
        );
        // 根据建议，抛出配置错误
        throw new Error(
          `[RedisLock] Configuration error: LockService not found in ${target.constructor.name}.`,
        );
      }

      let lockKey = options.keyPattern;
      try {
        lockKey = options.keyPattern.replace(/\{args\[(\d+)\](?:\.(\w+))?\}/g, (match, indexStr, prop) => {
          const index = parseInt(indexStr, 10);
          if (index < args.length) {
            const arg = args[index];
            if (prop && arg && typeof arg === 'object' && prop in arg) {
              return String(arg[prop]);
            }
            if (!prop && arg !== undefined) {
              return String(arg);
            }
          }
          loggerInstance.warn(`[RedisLock] Failed to substitute argument for placeholder: ${match} in key ${options.keyPattern}`);
          return match; // Return the original placeholder if substitution fails
        });
      } catch (e) {
        loggerInstance.error(`[RedisLock] Error generating lock key for ${target.constructor.name}.${propertyKey}: ${e.message}`, e.stack);
        // 如果键生成失败，根据 onFailure 策略处理或直接抛出错误
        if (options.onFailure === 'returnNull') {
          return null;
        }
        throw new Error(`[RedisLock] Failed to generate lock key for ${target.constructor.name}.${propertyKey}.`);
      } 

      const { ttl = 30, onFailure = 'throwError' } = options;
      let acquired = false;

      try {
        acquired = await lockServiceInstance.tryLock(lockKey, ttl);
      } catch (e) {
        loggerInstance.error(`[RedisLock] Error during tryLock for key ${lockKey}: ${e.message}`, e.stack);
        if (onFailure === 'throwError') {
          throw new LockAcquisitionError(`[RedisLock] Failed to attempt lock acquisition for key: ${lockKey} due to underlying service error.`);
        }
        return null;
      }

      if (acquired) {
        loggerInstance.debug(`[RedisLock] Lock acquired for key: ${lockKey}`);
        try {
          return await originalMethod.apply(self, args);
        } finally {
          try {
            await lockServiceInstance.unlock(lockKey);
            loggerInstance.debug(`[RedisLock] Lock released for key: ${lockKey}`);
          } catch (e) {
            loggerInstance.error(`[RedisLock] Error releasing lock for key ${lockKey}: ${e.message}`, e.stack);
            // 根据需求，这里可能需要决定是否向上抛出解锁失败的异常
            // 通常，即使解锁失败，原始方法的结果也应该返回，但记录错误是必要的
          }
        }
      } else {
        loggerInstance.warn(`[RedisLock] Failed to acquire lock for key: ${lockKey}`);
        if (onFailure === 'throwError') {
          throw new LockAcquisitionError(`[RedisLock] Failed to acquire lock for key: ${lockKey}`);
        }
        if (onFailure === 'returnNull') {
          return null;
        }
      }
      // Should not be reached if onFailure is 'throwError' or 'returnNull' and lock is not acquired.
      // However, to satisfy TypeScript's all-paths-return-value, and as a fallback.
      return undefined;
    };
    return descriptor;
  };
}