import 'reflect-metadata';
import { TaskLogicEnum } from '../enums/task-logic.enum.js'; // esm project, import local ts source code should using .js suffix

export const TASK_LOGIC_METADATA_KEY = Symbol('TaskLogicMetadataKey');

export function TaskLogic(logicId: TaskLogicEnum): MethodDecorator {
  return (target: object, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(TASK_LOGIC_METADATA_KEY, logicId, target[propertyKey as string]);
  };
}