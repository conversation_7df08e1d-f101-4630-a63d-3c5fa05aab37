import 'reflect-metadata';

export const TASK_PARAM_METADATA_KEY = 'task:param_mapping';

export interface TaskParamMapping {
  index: number;
  paramKey: string;
}

/**
 * Parameter decorator to map a task's `params` object property to a method parameter.
 * @param paramKey The key in the `task.params` object that should be mapped to this parameter.
 */
export function TaskParam(paramKey: string): ParameterDecorator {
  return (target: object, propertyKey: string | symbol | undefined, parameterIndex: number) => {
    if (!propertyKey) {
      // This should not happen for parameter decorators on methods
      return;
    }
    const existingParams: TaskParamMapping[] = Reflect.getOwnMetadata(
      TASK_PARAM_METADATA_KEY,
      target.constructor, // Store on the class constructor for the method
      propertyKey,
    ) || [];

    existingParams.push({ index: parameterIndex, paramKey });
    // Sort by index to ensure correct order when retrieving later
    existingParams.sort((a, b) => a.index - b.index);

    Reflect.defineMetadata(
      TASK_PARAM_METADATA_KEY,
      existingParams,
      target.constructor, // Store on the class constructor for the method
      propertyKey,
    );
  };
}