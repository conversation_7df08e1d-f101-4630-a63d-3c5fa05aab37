export enum TaskLogicEnum {
  FETCH_DATA = 'FETCH_DATA',
  PUBLISH_NEWS = 'PUBLISH_NEWS',
  RUN_AGENT = "RUN_AGENT",
  GET_NEWS_TOP = "GET_NEWS_TOP",
  FETCH_LINUX_DO_TOPIC = 'FETCH_LINUX_DO_TOPIC',
  FETCH_LINUX_DO_NEW_TOPIC_LIST = 'FETCH_LINUX_DO_NEW_TOPIC_LIST',
  UPDATE_LINUX_DO_NAV_ITEM = 'UPDATE_LINUX_DO_NAV_ITEM',
  UPDATE_LINUX_DO_NAV_ITEM_BATCH = 'UPDATE_LINUX_DO_NAV_ITEM_BATCH',
  RENEW_CURRENT_DATE = "RENEW_CURRENT_DATE",
  MESSAGE_PROVIDER = "MESSAGE_PROVIDER",
  SAVE_LINUX_DO_NOTE = "SAVE_LINUX_DO_NOTE",
  SAVE_LINUX_DO_NOTE_BATCH = "SAVE_LINUX_DO_NOTE_BATCH",
}

export interface TaskLogicItem {
  value: TaskLogicEnum;
  description: string;
}

export const TaskLogicList: TaskLogicItem[] = [
  {
    value: TaskLogicEnum.FETCH_DATA,
    description: '拉取数据',
  },
  {
    value: TaskLogicEnum.FETCH_LINUX_DO_TOPIC,
    description: '拉取LinuxDo指定话题',
  },
  {
    value: TaskLogicEnum.FETCH_LINUX_DO_NEW_TOPIC_LIST,
    description: '拉取LinuxDo最新全量话题',
  },
  {
    value: TaskLogicEnum.UPDATE_LINUX_DO_NAV_ITEM,
    description: '更新LinuxDo项目',
  },
  {
    value: TaskLogicEnum.UPDATE_LINUX_DO_NAV_ITEM_BATCH,
    description: '批量更新LinuxDo项目',
  },
  {
    value: TaskLogicEnum.PUBLISH_NEWS,
    description: 'Github发布新闻',
  },
  {
    value: TaskLogicEnum.RUN_AGENT,
    description: '执行AI-Agent',
  },
  {
    value: TaskLogicEnum.GET_NEWS_TOP,
    description: '获取最新榜单',
  },
  {
    value: TaskLogicEnum.RENEW_CURRENT_DATE,
    description: "更新当前日期"
  },
  {
    value: TaskLogicEnum.MESSAGE_PROVIDER,
    description: "获取参数化消息"
  },
  {
    value: TaskLogicEnum.SAVE_LINUX_DO_NOTE,
    description: "保存LinuxDo树洞笔记"
  },
  {
    value: TaskLogicEnum.SAVE_LINUX_DO_NOTE_BATCH,
    description: "批量保存LinuxDo树洞笔记"
  }
];