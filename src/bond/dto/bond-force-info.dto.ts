// src/bond/dto/bond-force-info.dto.ts
import { IsString, IsOptional, IsNumber, IsNotEmpty } from 'class-validator';

export class BondForceInfoDto {
  @IsString()
  @IsNotEmpty()
  bondNo: string;

  @IsString()
  @IsNotEmpty()
  bondName: string;

  @IsString()
  @IsOptional()
  forceStatus?: string;

  @IsString()
  @IsOptional()
  forceTip?: string;

  @IsString()
  @IsOptional()
  notForceEndTime?: string;

  @IsString()
  @IsOptional()
  forceRate?: string;

  @IsNumber()
  @IsOptional()
  forceCount?: number;

  @IsString()
  @IsOptional()
  forceStockPrice?: string;
}