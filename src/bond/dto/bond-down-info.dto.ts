// src/bond/dto/bond-down-info.dto.ts
import { IsString, IsOptional, IsNumber, IsNotEmpty } from 'class-validator';

export class BondDownInfoDto {
  @IsString()
  @IsNotEmpty()
  bondNo: string;

  @IsString()
  @IsNotEmpty()
  bondName: string;

  @IsString()
  @IsOptional()
  downStatus?: string;

  @IsString()
  @IsOptional()
  downTip?: string;

  @IsString()
  @IsOptional()
  notDownEndDate?: string;
  
  @IsNumber()
  @IsOptional()
  downCount?: number;

  @IsString()
  @IsOptional()
  downRate?: string;

  @IsString()
  @IsOptional()
  downStockPrice?: string;
}