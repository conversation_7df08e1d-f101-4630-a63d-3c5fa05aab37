// src/bond/dto/bond-resold-info.dto.ts
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class BondResoldInfoDto {
  @IsString()
  @IsNotEmpty()
  bondNo: string;

  @IsString()
  @IsNotEmpty()
  bondName: string;

  @IsString()
  @IsOptional()
  resoldStatus?: string;

  @IsString()
  @IsOptional()
  resoldTip?: string;

  @IsString()
  @IsOptional()
  resoldStartTime?: string;

  @IsString()
  @IsOptional()
  resoldRate?: string;

  @IsString()
  @IsOptional()
  resoldStockPrice?: string;
}