// src/bond/bond.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config'; // Added ConfigModule
import { BondController } from './bond.controller.js';
import { BondService } from './bond.service.js';
import { HttpModule } from '../common/http.module.js';

@Module({
  imports: [
    ConfigModule, // For accessing environment variables
    HttpModule,
  ],
  controllers: [BondController],
  providers: [BondService],
})
export class BondModule {}