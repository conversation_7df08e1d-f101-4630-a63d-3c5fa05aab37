// src/bond/bond.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config'; // Added ConfigModule
import { BondController } from './bond.controller.js';
import { BondService } from './bond.service.js';
import { AuthModule } from '../auth/auth.module.js';

@Module({
  imports: [
    HttpModule, // For BondService to make HTTP requests
    ConfigModule, // For accessing environment variables
    AuthModule,
  ],
  controllers: [BondController],
  providers: [BondService],
})
export class BondModule {}