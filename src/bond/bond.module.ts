// src/bond/bond.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config'; // Added ConfigModule
import { BondController } from './bond.controller.js';
import { BondService } from './bond.service.js';

@Module({
  imports: [
    ConfigModule, // For accessing environment variables
    HttpModule,
  ],
  controllers: [BondController],
  providers: [BondService],
})
export class BondModule {}