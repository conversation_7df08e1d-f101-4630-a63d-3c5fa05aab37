// src/bond/bond.controller.ts
import { Controller, Get, Logger, UseGuards } from '@nestjs/common'; // Added UseGuards
import { BondService } from './bond.service.js';
import { BondDownInfoDto } from './dto/bond-down-info.dto.js';
import { BondResoldInfoDto } from './dto/bond-resold-info.dto.js';
import { BondForceInfoDto } from './dto/bond-force-info.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js'; // Assuming standard AuthGuard
import { AuthPermissions } from '../common/decorators/auth.decorator.js'; // Assuming standard AuthPermissions decorator
import { PermissionLevel } from '../common/decorators/auth.enum.js'; // Assuming standard PermissionLevel enum

@Controller('/bond')
export class BondController {
  private readonly logger = new Logger(BondController.name);

  constructor(private readonly bondService: BondService) {}

  @Get('/downList')
  @UseGuards(AuthGuard) // Apply AuthGuard
  @AuthPermissions(PermissionLevel.READ_ONLY) // Set appropriate permissions
  async getDownList(): Promise<BondDownInfoDto[]> {
    this.logger.log('Endpoint /bond/downList called');
    return this.bondService.fetchDown();
  }

  @Get('/forceList')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getForceList(): Promise<BondForceInfoDto[]> {
    this.logger.log('Endpoint /bond/forceList called');
    return this.bondService.fetchForce();
  }

  @Get('/resoldList')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getResoldList(): Promise<BondResoldInfoDto[]> {
    this.logger.log('Endpoint /bond/resoldList called');
    return this.bondService.fetchResold();
  }
}
