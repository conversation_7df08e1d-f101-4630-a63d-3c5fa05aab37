// src/bond/bond.controller.ts
import { Controller, Get, Logger, All, Req, Res, HttpStatus, Post, Body } from '@nestjs/common';
import { Request, Response } from 'express';
import { BondService } from './bond.service.js';
import { BondDownInfoDto } from './dto/bond-down-info.dto.js';
import { BondResoldInfoDto } from './dto/bond-resold-info.dto.js';
import { BondForceInfoDto } from './dto/bond-force-info.dto.js';
import { ConfigService } from '@nestjs/config';
import { RawResponse } from '../common/decorators/raw-response.decorator.js'; // Added RawResponse decorator

@Controller('/bond')
export class BondController {
  private readonly logger = new Logger(BondController.name);

  constructor(
    private readonly bondService: BondService,
    private readonly configService: ConfigService,
  ) {}

  @Get('/downList')
  async getDownList(@Req() req: Request): Promise<BondDownInfoDto[]> {
    this.logger.log('Endpoint /bond/downList called');
    return this.bondService.fetchDown(req);
  }

  @Get('/forceList')
  async getForceList(@Req() req: Request): Promise<BondForceInfoDto[]> {
    this.logger.log('Endpoint /bond/forceList called');
    return this.bondService.fetchForce(req);
  }

  @Get('/resoldList')
  async getResoldList(@Req() req: Request): Promise<BondResoldInfoDto[]> {
    this.logger.log('Endpoint /bond/resoldList called');
    return this.bondService.fetchResold(req);
  }

  /**
   * Set cookies from frontend document.cookie format
   * Accepts document.cookie string and sets individual cookies
   */
  @Post('/setCookies')
  async setCookies(@Body('cookies') cookiesString: string, @Res() res: Response): Promise<void> {
    if (!cookiesString) {
      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Bad Request',
        message: 'cookies parameter is required',
      });
      return;
    }

    try {
      // Parse document.cookie format: "name1=value1; name2=value2; name3=value3"
      const cookiePairs = cookiesString.split(';').map(cookie => cookie.trim());

      this.logger.debug(`Total cookie pairs found: ${cookiePairs.length}`);
      this.logger.debug(`Cookie pairs: ${JSON.stringify(cookiePairs)}`);

      let setCookieCount = 0;
      for (const cookiePair of cookiePairs) {
        if (cookiePair) {
          const [name, ...valueParts] = cookiePair.split('=');
          const value = valueParts.join('='); // Handle values that contain '='

          if (name && value !== undefined && value !== '') {
            const cookieName = name.trim();
            const cookieValue = value.trim();

            this.logger.debug(`Processing cookie: ${cookieName}=${cookieValue}`);

            // Set cookie with appropriate options
            const cookieOptions = {
              httpOnly: true, // Prevent frontend access for security
              secure: this.configService.get<string>('NODE_ENV') === 'production',
              signed: false, // Don't sign cookies to preserve original values
              path: '/',
              maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
              encode: (value: string) => value, // Disable automatic URL encoding
            };
            res.cookie(cookieName, cookieValue, cookieOptions);
            setCookieCount++;

            this.logger.debug(`Set cookie #${setCookieCount}: ${cookieName}=${cookieValue.substring(0, 20)}...`);
          } else {
            this.logger.warn(`Skipped invalid cookie pair: "${cookiePair}" (name: "${name}", value: "${value}")`);
          }
        }
      }

      this.logger.log(`Successfully set ${setCookieCount} cookies`);

      res.status(HttpStatus.OK).json({
        message: 'Cookies set successfully',
        count: setCookieCount,
        totalPairs: cookiePairs.length,
      });

    } catch (error) {
      this.logger.error(`Error setting cookies: ${error.message}`, error.stack);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
        message: 'Failed to set cookies',
        details: error.message,
      });
    }
  }

  /**
   * Unified proxy endpoint for jisilu.cn requests
   * Handles both login (/data/webapi/account/login_process) and data requests (/data/*)
   */
  @All('/data/*path')
  @RawResponse()
  async fetchData(@Req() req: Request, @Res() res: Response): Promise<void> {

    // Extract the path from named wildcard parameter
    const pathArray = req.params.path;
    const targetPath = Array.isArray(pathArray) ? pathArray.join('/') : (pathArray || '');

    this.logger.debug(`Extracted targetPath: ${targetPath}`);

    if (!targetPath) {
      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid Path',
        message: 'Unable to determine target path',
        debug: {
          url: req.url,
          path: req.path,
          params: req.params
        }
      });
      return;
    }

    this.logger.log(`Jisilu proxy endpoint called: ${targetPath}`);

    try {
      // Use the service method for proxying
      const response = await this.bondService.fetchData(req, targetPath, req.method, req.body);

      // Forward all response headers (including set-cookie)
      Object.entries(response.headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'transfer-encoding') {
          res.set(key, value as string);
        }
      });

      // Return response with same status and data
      res.status(response.status).send(response.data);

    } catch (error) {
      this.logger.error(`Jisilu proxy error: ${error.message}`, error.stack);
      res.status(HttpStatus.BAD_GATEWAY).json({
        error: 'Proxy Error',
        message: 'Failed to proxy request to jisilu.cn',
        details: error.message,
      });
    }
  }

}
