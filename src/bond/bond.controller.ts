// src/bond/bond.controller.ts
import { <PERSON>, Get, Logger, UseGuards, All, Req, <PERSON><PERSON>, HttpStatus, Headers, Post, Body } from '@nestjs/common';
import { Request, Response } from 'express';
import { BondService } from './bond.service.js';
import { BondDownInfoDto } from './dto/bond-down-info.dto.js';
import { BondResoldInfoDto } from './dto/bond-resold-info.dto.js';
import { BondForceInfoDto } from './dto/bond-force-info.dto.js';
import { ConfigService } from '@nestjs/config';
import { RawResponse } from '../common/decorators/raw-response.decorator.js'; // Added RawResponse decorator
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';

@Controller('/bond')
export class BondController {
  private readonly logger = new Logger(BondController.name);

  constructor(
    private readonly bondService: BondService,
    private readonly configService: ConfigService,
  ) {}

  @Get('/downList')
  async getDownList(@Headers() headers: Record<string, string>): Promise<BondDownInfoDto[]> {
    this.logger.log('Endpoint /bond/downList called');
    return this.bondService.fetchDown(headers['cookie']);
  }

  @Get('/forceList')
  async getForceList(@Headers() headers: Record<string, string>): Promise<BondForceInfoDto[]> {
    this.logger.log('Endpoint /bond/forceList called');
    return this.bondService.fetchForce(headers['cookie']);
  }

  @Get('/resoldList')
  async getResoldList(@Headers() headers: Record<string, string>): Promise<BondResoldInfoDto[]> {
    this.logger.log('Endpoint /bond/resoldList called');
    return this.bondService.fetchResold(headers['cookie']);
  }

  /**
   * Set cookies from frontend document.cookie format
   * Accepts document.cookie string and sets individual cookies
   */
  @Post('/setCookies')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  async setCookies(@Body('cookies') cookiesString: string, @Res() res: Response): Promise<void> {
    if (!cookiesString) {
      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Bad Request',
        message: 'cookies parameter is required',
      });
      return;
    }

    try {
      // Parse document.cookie format: "name1=value1; name2=value2; name3=value3"
      const cookiePairs = cookiesString.split(';').map(cookie => cookie.trim());

      for (const cookiePair of cookiePairs) {
        if (cookiePair) {
          const [name, ...valueParts] = cookiePair.split('=');
          const value = valueParts.join('='); // Handle values that contain '='

          if (name && value !== undefined) {
            // Set cookie with appropriate options
            const cookieOptions = {
              httpOnly: true,
              secure: this.configService.get<string>('NODE_ENV') === 'production',
              signed: true,
              path: '/',
              maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
            };
            res.cookie(name.trim(), value.trim(), cookieOptions);

            this.logger.debug(`Set cookie: ${name.trim()}=${value.trim().substring(0, 20)}...`);
          }
        }
      }

      res.status(HttpStatus.OK).json({
        message: 'Cookies set successfully',
        count: cookiePairs.filter(pair => pair.includes('=')).length,
      });

    } catch (error) {
      this.logger.error(`Error setting cookies: ${error.message}`, error.stack);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal Server Error',
        message: 'Failed to set cookies',
        details: error.message,
      });
    }
  }

  /**
   * Unified proxy endpoint for jisilu.cn requests
   * Handles both login (/data/webapi/account/login_process) and data requests (/data/*)
   */
  @All('/data/*path')
  @RawResponse()
  async fetchData(@Req() req: Request, @Res() res: Response): Promise<void> {

    // Extract the path from named wildcard parameter
    const pathArray = req.params.path;
    const targetPath = Array.isArray(pathArray) ? pathArray.join('/') : (pathArray || '');

    this.logger.debug(`Extracted targetPath: ${targetPath}`);

    if (!targetPath) {
      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid Path',
        message: 'Unable to determine target path',
        debug: {
          url: req.url,
          path: req.path,
          params: req.params
        }
      });
      return;
    }

    this.logger.log(`Jisilu proxy endpoint called: ${targetPath}`);

    try {
      // Use the service method for proxying
      const response = await this.bondService.fetchData(req, targetPath, req.method, req.body);

      // Forward all response headers (including set-cookie)
      Object.entries(response.headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'transfer-encoding') {
          res.set(key, value as string);
        }
      });

      // Return response with same status and data
      res.status(response.status).send(response.data);

    } catch (error) {
      this.logger.error(`Jisilu proxy error: ${error.message}`, error.stack);
      res.status(HttpStatus.BAD_GATEWAY).json({
        error: 'Proxy Error',
        message: 'Failed to proxy request to jisilu.cn',
        details: error.message,
      });
    }
  }

}
