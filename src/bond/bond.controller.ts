// src/bond/bond.controller.ts
import { Controller, Get, Logger, UseGuards, All, Req, Res, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { BondService } from './bond.service.js';
import { BondDownInfoDto } from './dto/bond-down-info.dto.js';
import { BondResoldInfoDto } from './dto/bond-resold-info.dto.js';
import { BondForceInfoDto } from './dto/bond-force-info.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js'; // Assuming standard AuthGuard
import { AuthPermissions } from '../common/decorators/auth.decorator.js'; // Assuming standard AuthPermissions decorator
import { PermissionLevel } from '../common/decorators/auth.enum.js'; // Assuming standard PermissionLevel enum
import { RawResponse } from '../common/decorators/raw-response.decorator.js'; // Added RawResponse decorator

@Controller('/bond')
export class BondController {
  private readonly logger = new Logger(BondController.name);

  constructor(
    private readonly bondService: BondService,
  ) {}

  @Get('/downList')
  async getDownList(): Promise<BondDownInfoDto[]> {
    this.logger.log('Endpoint /bond/downList called');
    return this.bondService.fetchDown();
  }

  @Get('/forceList')
  async getForceList(): Promise<BondForceInfoDto[]> {
    this.logger.log('Endpoint /bond/forceList called');
    return this.bondService.fetchForce();
  }

  @Get('/resoldList')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  async getResoldList(): Promise<BondResoldInfoDto[]> {
    this.logger.log('Endpoint /bond/resoldList called');
    return this.bondService.fetchResold();
  }

  /**
   * Unified proxy endpoint for jisilu.cn requests
   * Handles both login (/data/webapi/account/login_process) and data requests (/data/*)
   */
  @All('data/*')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_ONLY)
  @RawResponse()
  async proxyToJisilu(@Req() req: Request, @Res() res: Response): Promise<void> {
    // Extract the path after /bond/data/
    const pathMatch = req.path.match(/\/bond\/data\/(.*)$/);
    const targetPath = pathMatch ? pathMatch[1] : '';

    if (!targetPath) {
      res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Invalid Path',
        message: 'Unable to determine target path',
      });
      return;
    }

    this.logger.log(`Jisilu proxy endpoint called: ${targetPath}`);

    try {
      // Use the service method for proxying
      const response = await this.bondService.proxyToJisilu(targetPath, req.method, req.body);

      // Forward all response cookies to client
      const setCookieHeaders = response.headers['set-cookie'];
      if (setCookieHeaders) {
        setCookieHeaders.forEach((cookie: string) => {
          res.append('Set-Cookie', cookie);
        });
      }

      // Forward other relevant headers
      Object.entries(response.headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'set-cookie' && key.toLowerCase() !== 'transfer-encoding') {
          res.set(key, value as string);
        }
      });

      // Return response with same status and data
      res.status(response.status).send(response.data);

    } catch (error) {
      this.logger.error(`Jisilu proxy error: ${error.message}`, error.stack);
      res.status(HttpStatus.BAD_GATEWAY).json({
        error: 'Proxy Error',
        message: 'Failed to proxy request to jisilu.cn',
        details: error.message,
      });
    }
  }

}
