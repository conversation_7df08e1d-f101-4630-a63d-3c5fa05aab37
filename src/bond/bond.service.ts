// src/bond/bond.service.ts
import iconv from 'iconv-lite';
import * as cheerio from 'cheerio';
import { firstValueFrom } from 'rxjs';
import { Inject, Injectable, Logger, Scope } from '@nestjs/common'; // Scope and Inject for REQUEST
import { REQUEST } from '@nestjs/core'; // For REQUEST scope
import { Request } from 'express'; // For Express request type
import { ConfigService } from '@nestjs/config'; // Added ConfigService
// DTOs will be imported when parse methods are fully implemented
import { BondDownInfoDto } from './dto/bond-down-info.dto.js';
import { BondResoldInfoDto } from './dto/bond-resold-info.dto.js';
import { BondForceInfoDto } from './dto/bond-force-info.dto.js';
import { HttpService } from '@nestjs/axios';

@Injectable({ scope: Scope.REQUEST }) // Set scope to REQUEST
export class BondService {
  private readonly logger = new Logger(BondService.name);

  private readonly FORCE_URL = 'https://www.ninwin.cn/index.php?m=cb&a=call';
  private readonly RESOLD_URL = 'https://www.ninwin.cn/index.php?m=cb&a=put';
  private readonly DOWN_URL = 'https://www.ninwin.cn/index.php?m=cb&a=revise_down';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService, // Added ConfigService injection
    @Inject(REQUEST) private readonly request: Request, // Inject Request object
  ) {
    this.logger.log('BondService initialized (Request Scope)');
  }

  private async fetchNinwinPage<T>(
    url: string,
    parser: (body: string) => T,
  ): Promise<T> {
    this.logger.log(`Fetching Ninwin page: ${url}`);
    const userCookie = this.request.headers.cookie || '';
    this.logger.debug(`Forwarding user cookie: ${userCookie ? userCookie.substring(0, 50) + '...' : 'empty'}`);

    try {
      const response = await firstValueFrom(
        this.httpService.get(url, {
          timeout: 30000,
          headers: {
            Cookie: userCookie,
            // Add other headers if ninwin.cn requires them, e.g., User-Agent
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
          responseType: 'arraybuffer', // Crucial for getting raw buffer for iconv
        }),
      );

      const responseBuffer = Buffer.from(response.data);
      const decodedBody = iconv.decode(responseBuffer, 'gb2312');
      this.logger.log(`Successfully fetched and decoded page from ${url}`);
      return parser(decodedBody);
    } catch (error) {
      this.logger.error(`Failed to fetch or parse page ${url}: ${error.message}`, error.stack);
      // Consider throwing a more specific HttpException if this service is directly used by a controller
      // that doesn't catch and re-wrap errors.
      throw new Error(`Failed to fetch or parse page ${url}: ${error.message}`);
    }
  }

  // In BondService class
  private parseDown(body: string): BondDownInfoDto[] {
    this.logger.debug('Parsing "down" bonds data.');
    const $ = cheerio.load(body);
    const bondList: BondDownInfoDto[] = [];

    $("#cb_hq tr[data-cb_code]").each((i, elem) => {
      const bondInfo = new BondDownInfoDto();
      bondInfo.bondNo = $(elem).children("td.bond_code_id").first().text().trim();
      bondInfo.bondName = $(elem).children("td.cb_name_id").first().text().trim();
      bondInfo.downStatus = $(elem).children("td.count[title]").first().text().replace(/\d/g, '').trim();
      bondInfo.downTip = $(elem).children("td.count[title]").first().attr("title")?.trim();
      bondInfo.notDownEndDate = $(elem).children("td.bond_date_id").first().text().trim();
      
      const downCountText = $(elem).children("td.count").eq(1).text().trim();
      const forceDayLeftText = $(elem).find("td.count[title] span[style='']").text().trim();
      bondInfo.downCount = parseInt(downCountText, 10) - (parseInt(forceDayLeftText, 10) || 0);
      if (isNaN(bondInfo.downCount)) bondInfo.downCount = undefined;


      bondInfo.downRate = $(elem).children("td.count").eq(3).text().trim();
      bondInfo.downStockPrice = $(elem).children("td.cb_strike_id").eq(1).text().trim();
      bondList.push(bondInfo);
    });
    this.logger.log(`Parsed ${bondList.length} "down" bonds.`);
    return bondList;
  }

  private parseResold(body: string): BondResoldInfoDto[] {
    this.logger.debug('Parsing "resold" bonds data.');
    const $ = cheerio.load(body);
    const bondList: BondResoldInfoDto[] = [];

    $("#cb_hq tr[data-cb_code]").each((i, elem) => {
      const bondInfo = new BondResoldInfoDto();
      bondInfo.bondNo = $(elem).children("td.bond_code_id").first().text().trim();
      bondInfo.bondName = $(elem).children("td.cb_name_id").first().text().trim();
      bondInfo.resoldStatus = $(elem).children("td.count[title]").first().text().replace(/\d/g, '').trim();
      bondInfo.resoldTip = $(elem).children("td.count[title]").first().attr("title")?.trim();
      bondInfo.resoldStartTime = $(elem).children("td.bond_date_id").first().text().trim();
      bondInfo.resoldRate = $(elem).children("td.count").eq(2).text().trim();
      bondInfo.resoldStockPrice = $(elem).children("td.cb_strike_id").eq(1).text().trim();
      bondList.push(bondInfo);
    });
    this.logger.log(`Parsed ${bondList.length} "resold" bonds.`);
    return bondList;
  }

  private parseForce(body: string): BondForceInfoDto[] {
    this.logger.debug('Parsing "force" bonds data.');
    const $ = cheerio.load(body);
    const bondList: BondForceInfoDto[] = [];

    $("#cb_hq tr[data-cb_code]").each((i, elem) => {
      const bondInfo = new BondForceInfoDto();
      bondInfo.bondNo = $(elem).children("td.bond_code_id").first().text().trim();
      bondInfo.bondName = $(elem).children("td.cb_name_id").first().text().trim();
      bondInfo.forceStatus = $(elem).children("td.count[title]").first().text().replace(/\d/g, '').trim();
      bondInfo.forceTip = $(elem).children("td.count[title]").first().attr("title")?.trim();
      bondInfo.notForceEndTime = $(elem).children("td.bond_date_id").eq(1).text().trim();
      bondInfo.forceRate = $(elem).children("td.count").eq(3).text().trim();
      
      const forceCountText = $(elem).children("td.count").eq(1).text().trim();
      const forceDayLeftText = $(elem).find("td.count[title] span[style='']").text().trim();
      bondInfo.forceCount = parseInt(forceCountText, 10) - (parseInt(forceDayLeftText, 10) || 0);
       if (isNaN(bondInfo.forceCount)) bondInfo.forceCount = undefined;

      bondInfo.forceStockPrice = $(elem).children("td.cb_strike_id").eq(1).text().trim();
      bondList.push(bondInfo);
    });
    this.logger.log(`Parsed ${bondList.length} "force" bonds.`);
    return bondList;
  }

  async fetchDown(): Promise<BondDownInfoDto[]> {
    return this.fetchNinwinPage(this.DOWN_URL, this.parseDown.bind(this));
  }

  async fetchResold(): Promise<BondResoldInfoDto[]> {
    return this.fetchNinwinPage(this.RESOLD_URL, this.parseResold.bind(this));
  }

  async fetchForce(): Promise<BondForceInfoDto[]> {
    return this.fetchNinwinPage(this.FORCE_URL, this.parseForce.bind(this));
  }

  /**
   * Generic proxy method for jisilu.cn requests
   * @param targetPath - The path to proxy to (e.g., 'data/cbnew/cb_list/')
   * @param method - HTTP method
   * @param requestBody - Request body for POST requests
   * @returns Axios response object
   */
  async fetchData(targetPath: string, method: string, requestBody?: any): Promise<any> {
    const targetUrl = `https://www.jisilu.cn/${targetPath}`;
    this.logger.log(`Proxying ${method} request to: ${targetUrl}`);

    const userCookie = this.request.headers.cookie || '';
    this.logger.debug(`Forwarding user cookie: ${userCookie ? userCookie.substring(0, 50) + '...' : 'empty'}`);

    // Check if this is a login request and prepare special data
    let requestData = requestBody;
    if (targetPath === 'webapi/account/login_process') {
      const username = this.configService.get<string>('STOCK_USERNAME');
      const password = this.configService.get<string>('STOCK_PASSWORD');

      if (!username || !password) {
        throw new Error('Login credentials not configured in environment variables');
      }

      // Prepare login data
      requestData = new URLSearchParams({
        return_url: 'https://www.jisilu.cn/',
        user_name: username,
        password: password,
        auto_login: '1',
        aes: '1',
      });

      this.logger.log('Preparing login request with credentials from environment variables');
    }

    try {
      const response = await firstValueFrom(
        this.httpService.request({
          method: method as any,
          url: targetUrl,
          headers: {
            // Forward all original headers first
            ...this.request.headers,
            // Override specific headers as per nginx configuration
            'Host': 'www.jisilu.cn',
            'X-Real-IP': this.request.ip || this.request.get('x-real-ip') || '',
            'X-Forwarded-For': this.request.get('x-forwarded-for') || this.request.ip || '',
            'REMOTE-HOST': this.request.ip || '',
            // Set content type for login requests
            ...(targetPath === 'webapi/account/login_process' && {
              'Content-Type': 'application/x-www-form-urlencoded',
            }),
          },
          data: requestData,
          timeout: 30000,
          validateStatus: () => true, // Accept all status codes
          responseType: 'arraybuffer', // Get raw response data
        }),
      );

      this.logger.log(`Proxy response: ${response.status} for ${targetUrl}`);
      return response;
    } catch (error) {
      this.logger.error(`Proxy error for ${targetUrl}: ${error.message}`, error.stack);
      throw new Error(`Failed to proxy request to ${targetUrl}: ${error.message}`);
    }
  }
}