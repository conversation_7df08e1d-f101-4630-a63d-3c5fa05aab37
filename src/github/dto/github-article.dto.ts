// src/github/dto/github-article.dto.ts
import { IsString, IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ArticleListItemDto {
  @IsString()
  title: string;

  @IsString()
  link: string;

  @IsString()
  path: string;

  @IsString()
  type: 'dir' | 'file'; // Or more specific types
}

export class GithubArticleDto {

  @IsString()
  data?: string; // markdown text

  @IsString()
  path: string;

}