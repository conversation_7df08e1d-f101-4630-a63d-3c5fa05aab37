// src/github/github.service.ts
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Octokit } from '@octokit/rest';
import { createAppAuth } from '@octokit/auth-app';
import { Endpoints } from "@octokit/types";
import { RedisCache } from '../common/decorators/redis-cache.decorator.js';
import { RedisService } from '../redis/redis.service.js';
import { MonitorService } from '../monitor/monitor.service.js';
import { TaskLogic } from '../common/decorators/task-logic.decorator.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { TaskParam } from '../common/decorators/task-param.decorator.js';
import { HttpsProxyAgent } from 'https-proxy-agent';

// Constants for repository names can remain if they are not sensitive
const OWNER = "pnparadise";

@Injectable()
export class GithubService {
  private readonly logger = new Logger(GithubService.name);

  private octokitAppAuth: Octokit;
  private appAuthInitializationPromise: Promise<void>;
  private GITHUB_APP_ID: string;
  private GITHUB_APP_INSTALLATION_ID: number;
  private GITHUB_APP_CLIENT_ID: string;
  private GITHUB_APP_CLIENT_SECRET: string;
  private GITHUB_APP_PRIVATE_KEY: string;
  private HTTP_PROXY: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly monitorService: MonitorService,
  ) {
    this.logger.log('GithubService initializing with ConfigService...');

    // Load all GitHub App credentials
    this.GITHUB_APP_ID = this.configService.get<string>('GITHUB_APP_ID') as string;
    const installationIdStr = this.configService.get<string>('GITHUB_APP_INSTALLATION_ID') as string;
    this.GITHUB_APP_INSTALLATION_ID = parseInt(installationIdStr, 10);
    this.GITHUB_APP_CLIENT_ID = this.configService.get<string>('GITHUB_APP_CLIENT_ID') as string;
    this.GITHUB_APP_CLIENT_SECRET = this.configService.get<string>('GITHUB_APP_CLIENT_SECRET') as string;
    this.GITHUB_APP_PRIVATE_KEY = (this.configService.get<string>('GITHUB_APP_PRIVATE_KEY') as string)?.replace(/\\n/g, '\n');
    this.HTTP_PROXY = this.configService.get<string>('HTTP_PROXY') as string;

    this.logger.log('GithubService constructor finished. Initializing Octokit App Auth...');
    this.validateConfig();
    this.appAuthInitializationPromise = this.initializeOctokitAppAuth();
  }

  private async initializeOctokitAppAuth(): Promise<void> {
    try {

      this.octokitAppAuth = new Octokit({
        authStrategy: createAppAuth,
        auth: {
          appId: this.GITHUB_APP_ID,
          privateKey: this.GITHUB_APP_PRIVATE_KEY,
          installationId: this.GITHUB_APP_INSTALLATION_ID,
          clientId: this.GITHUB_APP_CLIENT_ID,
          clientSecret: this.GITHUB_APP_CLIENT_SECRET,
        },
        timeZone: 'Asia/Shanghai',
        baseUrl: 'https://api.github.com',
        request: {
          timeout: 30000,
          agent: this.HTTP_PROXY ? new HttpsProxyAgent(this.HTTP_PROXY) : undefined
        },
      });
      this.logger.log(
        'Octokit App Auth client initialized successfully with custom fetch.',
      );
    } catch (error) {
      this.logger.error(
        'Error during Octokit App Auth client initialization:',
        error,
      );
      throw error;
    }
  }

  private validateConfig() {
     const requiredAppKeys = [
         { key: 'GITHUB_APP_ID', value: this.GITHUB_APP_ID },
         { key: 'GITHUB_APP_INSTALLATION_ID', value: this.GITHUB_APP_INSTALLATION_ID },
         { key: 'GITHUB_APP_CLIENT_ID', value: this.GITHUB_APP_CLIENT_ID },
         { key: 'GITHUB_APP_CLIENT_SECRET', value: this.GITHUB_APP_CLIENT_SECRET },
         { key: 'GITHUB_APP_PRIVATE_KEY', value: this.GITHUB_APP_PRIVATE_KEY },
     ];

     let missingKeys: string[] = [];
     [...requiredAppKeys].forEach(item => {
         if (!item.value || (typeof item.value === 'number' && isNaN(item.value))) {
             missingKeys.push(item.key);
         }
     });

     if (missingKeys.length > 0) {
         const message = `Missing critical GitHub environment variables: ${missingKeys.join(', ')}. Please set them in your .env file.`;
         this.logger.error(message);
         throw new Error(message);
     }
     this.logger.log('GitHub configuration validated successfully.');
  }

  async client(repo: string): Promise<Octokit> {
    this.logger.debug(`Getting Octokit client for repo: ${repo}`);
    await this.appAuthInitializationPromise;
    if (!this.octokitAppAuth) {
      this.logger.error('CRITICAL: octokitAppAuth is still not initialized after awaiting appAuthInitializationPromise.');
      throw new Error('Octokit App Auth client failed to initialize.');
    }
    this.logger.debug('Using App Auth Octokit client.');
    return this.octokitAppAuth;
  }

  @RedisCache({ keyPattern: 'github:{args[0]}:{args[1]}' })
  async fetchArticle(repo: string, path: string): Promise<Endpoints["GET /repos/{owner}/{repo}/contents/{path}"]["response"]["data"]> {
    const actualPath = path || "";
    this.logger.log(`GithubService: fetchArticle called. Repo: '${repo}', Original Path: '${path}', Actual Path used: '${actualPath}'`);

    try {
      const client = await this.client(repo);
      this.logger.debug(`GithubService: Octokit client obtained for repo '${repo}'. Attempting to fetch content for path '${actualPath}' from owner '${OWNER}'.`);

      const response = await client.rest.repos.getContent({
        owner: OWNER,
        repo,
        path: actualPath,
        mediaType: { format: 'object' },
      });

      this.logger.log(`GithubService: getContent for repo '${repo}', path '${actualPath}' returned status ${response.status}.`);
      return response.data;
    } catch (error) {
      this.logger.error(`GithubService: Error in fetchArticle for repo '${repo}', path '${actualPath}'. Status: ${error.status}, Message: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'Failed to fetch article content from GitHub',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 清除路径缓存（文件或目录）
   * @param repo - 仓库名称
   * @param path - 文件或目录路径
   */
  async cleanCache(repo: string, path: string): Promise<void> {
    
    // 清除目录下所有文件的缓存（包括目录本身）
    const pattern = `github:${repo}:${path}*`;
    this.logger.log(`Clearing directory cache for pattern: ${pattern}`);

    try {
      const keys = await this.redisService.keys(pattern);
      if (keys.length > 0) {
        for (const key of keys) {
          await this.redisService.del(key);
        }
        this.logger.log(`Successfully cleared ${keys.length} cache keys for pattern: ${pattern}`);
      } else {
        this.logger.log(`No cache keys found for pattern: ${pattern}`);
      }
    } catch (error) {
      this.logger.error(`Failed to clear cache for pattern: ${pattern}`, error.stack);
    }
  
  }

  async cleanDataCache(payload: any): Promise<void> {
    this.logger.log(`Handling GitHub push event for repo: ${payload?.repository?.name}`);
    if (!payload || !payload.repository || !payload.commits) {
      this.logger.warn('Invalid payload received for webhook push event.');
      return;
    }

    const repoName = payload.repository.name;
    const changedFiles: string[] = [];

    for (const commit of payload.commits) {
      if (commit.added) {
        changedFiles.push(...commit.added);
      }
      if (commit.removed) {
        changedFiles.push(...commit.removed);
      }
      if (commit.modified) {
        changedFiles.push(...commit.modified);
      }
    }

    if (changedFiles.length === 0) {
      this.logger.log('No changed files found in the push event.');
      await this.monitorService.sendSimpleTextMessage(`GitHub repo ${repoName} received a push event, but no files were changed.`);
      return;
    }

    const uniqueChangedFiles = [...new Set(changedFiles)];
    const allKeysToClear = new Set<string>();

    for (const filePath of uniqueChangedFiles) {
      allKeysToClear.add(`github:${repoName}:${filePath}`);
      this.logger.log(`Marked Redis key for file deletion: github:${repoName}:${filePath}`);

      // 清除所有层级目录的 Redis keys
      const pathParts = filePath.split('/');
      
      // 从最深层开始，逐级向上清除每个目录层级
      for (let i = pathParts.length - 1; i > 0; i--) {
        const directoryPath = pathParts.slice(0, i).join('/');
        allKeysToClear.add(`github:${repoName}:${directoryPath}`);
        this.logger.log(`Marked Redis key for directory deletion: github:${repoName}:${directoryPath}`);
      }
      
      // 始终清除根目录
      allKeysToClear.add(`github:${repoName}:`);
      this.logger.log(`Marked Redis key for repo root deletion: github:${repoName}:`);
    }

    const redisKeysToDelete = Array.from(allKeysToClear);

    try {
      if (redisKeysToDelete.length > 0) {
        const deleteCount = await this.redisService.del(redisKeysToDelete);
        this.logger.log(`Successfully deleted ${deleteCount} Redis keys for repo ${repoName}.`);
      }

      const message = `GitHub repo \`${repoName}\` push event processed.\nCleared cache for ${uniqueChangedFiles.length} files:\n\`\`\`\n${uniqueChangedFiles.join('\n')}\n\`\`\``;
      await this.monitorService.sendSimpleTextMessage(message);
      this.logger.log(`Successfully sent notification to Telegram for repo ${repoName}.`);

    } catch (error) {
      this.logger.error(`Error processing webhook for repo ${repoName}: ${error.message}`, error.stack);
      await this.monitorService.sendSimpleTextMessage(`Error processing GitHub push event for repo ${repoName}: ${error.message}`);
    }
  }

  @TaskLogic(TaskLogicEnum.PUBLISH_NEWS)
  public async commitFile(
    @TaskParam("repo") repo: string,
    @TaskParam("path") path: string,
    @TaskParam("content") content: string,
    @TaskParam("message") message: string,
  ): Promise<Endpoints['PUT /repos/{owner}/{repo}/contents/{path}']['response']> {
    this.logger.log(
      `Attempting to push file to ${repo}/${path}`,
    );
    const client = await this.client(repo);

    const encodedContent = Buffer.from(content).toString('base64');

    const response = await client.rest.repos.createOrUpdateFileContents({
      owner: OWNER,
      repo,
      path,
      message,
      content: encodedContent
    });
    this.logger.log(
      `Successfully created/updated file ${path} in ${repo}/${path}. Commit SHA: ${response.data.commit.sha}`,
    );

    return response;
  }
}