// src/github/github.controller.ts
import { <PERSON>, <PERSON>, Lo<PERSON>, <PERSON>m, Query, <PERSON>s, Req, Post, Body, UseGuards, HttpException, HttpStatus, RawBodyRequest, Headers, NotFoundException } from '@nestjs/common'; // Added Headers, NotFoundException
import { GithubService } from './github.service.js';
import { GithubArticleService } from './services/github-article.service.js'; // Added
import { Response, Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { Webhooks } from '@octokit/webhooks';
import { OctokitResponse, Endpoints } from "@octokit/types";
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';
import { CurrentUser } from '../auth/decorators/current-user.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { ReindexPathDto } from './dto/reindex-path.dto.js'; // Added

@Controller('/github')
export class GithubController {
  private readonly logger = new Logger(GithubController.name);
  private readonly webhooks: Webhooks; // Keep if still used for other events or prefer its parsing

  constructor(
    private readonly githubService: GithubService,
    private readonly githubArticleService: GithubArticleService, // Injected
    private readonly configService: ConfigService,
  ) {
    const secret = this.configService.get<string>('API_KEY_READ_WRITE'); 
    if (!secret) {
      this.logger.error('API_KEY_READ_WRITE is not configured. Webhook verification will fail.');
      this.webhooks = new Webhooks({ secret: 'dummy_secret_due_to_missing_env_var' });
    } else {
      this.webhooks = new Webhooks({ secret });
    }
  }

  @Get('/:repo/articles')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  async getArticles(
    @Param('repo') repo: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @CurrentUser() user: AuthenticatedUser
  ): Promise<{ articles: any[], total: number, page: number, limit: number, totalPages: number }> {
    this.logger.debug(`获取文章列表，仓库: ${repo}, 页码: ${page}, 每页数量: ${limit}`);
    
    if ("gitbook" !== repo && (!user || user.provider !== "github" || user.username !== "pnparadise")) {
      throw new HttpException("资源未授权", HttpStatus.FORBIDDEN);
    }

    const pageNum = Math.max(1, parseInt(page, 10) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit, 10) || 10)); // 限制每页最多100条

    const result = await this.githubArticleService.getArticles(repo, pageNum, limitNum);
    
    return {
      articles: result.articles,
      total: result.total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(result.total / limitNum)
    };
  }

  @Post('/:repo/reindex') // 统一的重构索引接口
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async reindexPath(
    @Param('repo') repo: string,
    @Body() reindexDto: ReindexPathDto,
  ): Promise<{ message: string; success: boolean; repo: string; path: string; indexed?: number; errors?: number; article?: any }> {
    this.logger.log(`Reindex request for repo: ${repo}, path: ${reindexDto.path}, refresh: ${reindexDto.refresh}`);

    try {
      // 如果需要刷新缓存，直接清除缓存
      if (reindexDto.refresh) {
        await this.githubService.cleanCache(repo, reindexDto.path);
      }

      // 直接调用 reindexPath 方法，它已经支持文件和目录
      const result = await this.githubArticleService.reindexPath(repo, reindexDto.path);

      return {
        message: `Reindex process completed for ${repo}/${reindexDto.path}.`,
        success: true,
        repo: repo,
        path: reindexDto.path,
        indexed: result.indexed,
        errors: result.errors
      };
    } catch (error) {
      this.logger.error(`Error reindexing path ${repo}/${reindexDto.path}: ${error.message}`, error.stack);
      return {
        message: `Error reindexing path: ${repo}/${reindexDto.path} - ${error.message}`,
        success: false,
        repo: repo,
        path: reindexDto.path
      };
    }
  }


  @Get('/:repo/*')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  async getRepoContent(
    @Param('repo') repo: string,
    @Req() request: Request,
    @CurrentUser() user: AuthenticatedUser
  ): Promise<Endpoints['GET /repos/{owner}/{repo}/contents/{path}']['response']['data']>{
    this.logger.debug(repo, user)
    if("gitbook" !== repo && (!user || user.provider !== "github" || user.username !== "pnparadise")) {
      throw new HttpException("资源未授权", HttpStatus.FORBIDDEN);
    }
    const baseRoutePath = `/github/${repo}/`;
    let extractedPath = "";

    if (request.url.startsWith(baseRoutePath)) {
      extractedPath = request.url.substring(baseRoutePath.length);
    } else {
      // This case should ideally not happen if route matching is correct
      // but as a fallback, consider the part of URL after /github/
      const githubPrefix = "/github/";
      if(request.url.startsWith(githubPrefix)) {
        const pathAfterGithub = request.url.substring(githubPrefix.length);
        const firstSlashIndex = pathAfterGithub.indexOf('/');
        if (firstSlashIndex !== -1 && pathAfterGithub.substring(0, firstSlashIndex) === repo) {
           extractedPath = pathAfterGithub.substring(firstSlashIndex + 1);
        }
      }
    }
    
    // Remove query parameters from extractedPath
    const queryIndex = extractedPath.indexOf('?');
    if (queryIndex !== -1) {
      extractedPath = extractedPath.substring(0, queryIndex);
    }

    const actualPath = decodeURIComponent(extractedPath);

    this.logger.log(`GithubController: getRepoContent called. Repo: '${repo}', Manually Extracted Path: '${actualPath}', Request URL: '${request.url}'`);
    
    return await this.githubService.fetchArticle(repo, actualPath);
  }

 
  @Post('/:repo/webhook')
  async handleWebhook(
    @Param('repo') repo: string,
    @Body() payload: any, // NestJS parsed body, for the service layer
    @Req() request: RawBodyRequest<Request>, // To access rawBody for signature verification
  ): Promise<void> {
    const githubEvent = request.headers['x-github-event'] as string;
    const signature = request.headers['x-hub-signature-256'] as string;

    this.logger.log(`Received webhook event: ${githubEvent} for repo: ${repo}, signature: ${signature}`);

    if (!signature) {
      this.logger.warn(`Request for repo ${repo} is missing X-Hub-Signature-256 header.`);
      throw new HttpException('Missing X-Hub-Signature-256 header', HttpStatus.UNAUTHORIZED);
    }
    
    if (!request.rawBody) {
      this.logger.error('Raw body is not available for signature verification. Ensure NestJS is configured with rawBody: true in main.ts.');
      throw new HttpException('Raw body is required for signature verification', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const bodyString = request.rawBody.toString('utf-8');

    try {
      const isValid = await this.webhooks.verify(bodyString, signature);
      if (!isValid) {
        this.logger.warn(`Invalid signature for webhook event for repo ${repo}.`);
        throw new HttpException('Invalid signature', HttpStatus.FORBIDDEN);
      }
    } catch (error) {
        this.logger.error(`Error during webhook signature verification for repo ${repo}: ${error.message}`, error.stack);
        // @octokit/webhooks might throw an error for various reasons (e.g. malformed signature)
        throw new HttpException(`Signature verification failed: ${error.message}`, HttpStatus.BAD_REQUEST);
    }
    
    this.logger.log(`Webhook signature verified successfully for repo: ${repo}`);

    // The 'payload' from @Body() is already parsed JSON.
    // No need to JSON.parse(bodyString) again if @Body() is used for the payload.
    
    if (githubEvent === 'push') {
      // 处理 push 事件 - 委托给 article service，它会处理所有逻辑

      // 首先调用原有的 githubService 处理逻辑（如缓存清理等）
      await this.githubService.cleanDataCache(payload);

      await this.githubArticleService.buildArticleIndexFromWebhook(payload);

    } else if (githubEvent === 'ping') {
        this.logger.log('Received ping event. Replying successfully.');
    } else {
      this.logger.log(`Ignoring webhook event: ${githubEvent} for article indexing as it is not a 'push' or 'ping' event.`);
    }
    // GitHub expects a 2xx response to acknowledge receipt of the webhook.
    // Implicitly returns 200 OK if no error is thrown.
  }






}