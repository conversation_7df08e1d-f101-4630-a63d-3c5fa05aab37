import { BaseDataService } from '../../common/services/base-data.service.js';
import { ArticleMeta } from '../dto/article-meta.dto.js';
import { RedisService } from '../../redis/redis.service.js';

/**
 * 为特定 repo 的数据服务类
 * 每个实例绑定到特定的 GitHub 仓库，使用 path 作为文章的唯一标识符
 */
export class RepoArticleDataService extends BaseDataService<ArticleMeta> {
  constructor(
    private readonly repo: string,
    redisService: RedisService
  ) {
    super(ArticleMeta, redisService);
  }

  protected getKeyPrefix(): string {
    return `${this.entityName}:${this.repo}`;
  }

  protected async generateId(entity: Partial<ArticleMeta>): Promise<string> {
    // 使用 path 作为 ID
    if (!entity.path) {
      throw new Error('ArticleMeta must have a path to generate ID');
    }
    return entity.path;
  }
}
