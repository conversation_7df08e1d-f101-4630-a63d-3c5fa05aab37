// src/github/github.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios'; // For HttpService if needed by GithubService directly
import { GithubController } from './github.controller.js';
import { GithubService } from './github.service.js';
import { RedisModule } from '../redis/redis.module.js';
import { AuthModule } from '../auth/auth.module.js';
import { MonitorModule } from '../monitor/monitor.module.js';
import { GithubArticleService } from './services/github-article.service.js';


@Module({
  imports: [
    HttpModule, // For OAuth token exchange etc.
    RedisModule, // For caching GitHub API responses
    AuthModule,
    MonitorModule, // For sending Telegram notifications
  ],
  controllers: [GithubController],
  providers: [GithubService, GithubArticleService],
  exports: [GithubService, GithubArticleService],
})
export class GithubModule {}
