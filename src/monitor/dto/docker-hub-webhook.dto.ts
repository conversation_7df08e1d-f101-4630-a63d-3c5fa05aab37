import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, IsString, ValidateNested, IsOptional } from 'class-validator';

// 只包含我们需要验证和使用的字段

class PushDataDto {
  @IsString()
  @IsNotEmpty()
  tag: string;

  // 添加 pushed_at 以便将来可能使用
  @IsNotEmpty()
  pushed_at: number;

  @IsString()
  @IsNotEmpty()
  pusher: string;
}

class RepositoryDto {
  @IsString()
  @IsNotEmpty()
  repo_name: string; // e.g., "svendowideit/testhook"

  @IsString()
  @IsNotEmpty()
  name: string; // e.g., "testhook"

  @IsString()
  @IsNotEmpty()
  namespace: string; // e.g., "svendowideit"

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsNotEmpty()
  repo_url: string;

  @IsString()
  @IsNotEmpty()
  status: string; // e.g., "Active"
}

export class DockerHubWebhookDto {
  @IsObject()
  @ValidateNested()
  @Type(() => PushDataDto)
  @IsNotEmpty()
  push_data: PushDataDto;

  @IsObject()
  @ValidateNested()
  @Type(() => RepositoryDto)
  @IsNotEmpty()
  repository: RepositoryDto;

  // callback_url 可能是可选的，或者我们不需要验证它
  @IsString()
  @IsOptional()
  callback_url?: string;
}