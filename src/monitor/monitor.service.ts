// src/monitor/monitor.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { DockerHubWebhookDto } from './dto/docker-hub-webhook.dto.js'; // 添加 DTO 导入

@Injectable()
export class MonitorService {
  private readonly logger = new Logger(MonitorService.name);
  private botToken: string | undefined;
  private chatId: string | undefined;
  private readonly TELEGRAM_API_BASE_URL = 'https://api.telegram.org';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.botToken = this.configService.get<string>('TELEGRAM_BOT_TOKEN');
    this.chatId = this.configService.get<string>('TELEGRAM_CHAT_ID');

    if (!this.botToken || !this.chatId) {
      this.logger.error('Telegram Bot Token or Chat ID is not configured. Monitoring will be disabled.');
      // Optionally throw an error if these are critical for app startup,
      // or just log and disable the functionality.
    } else {
      this.logger.log('MonitorService initialized with Telegram configuration.');
    }
  }


  

  async sendErrorMessageToTelegram(error: Error, contextInfo?: string): Promise<void> {
    if (!this.botToken || !this.chatId) {
      this.logger.warn('Telegram monitoring is disabled due to missing configuration. Cannot send error message.');
      return;
    }

    // For MarkdownV2, certain characters must be escaped: '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'
    // This is a simplified escaper. A more robust one would be needed for all cases.
    // For stack traces, backticks and periods are common.
    const escapeMarkdownV2 = (text: string) => {
        if (!text) return ''; // Handle undefined or null text
        return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
    };

    // Re-construct message with escaped parts for MarkdownV2
    let markdownMessage = `*🚨 Unhandled Application Error 🚨*\n\n`;
    if (contextInfo) {
        markdownMessage += `*Context:* ${escapeMarkdownV2(contextInfo)}\n`;
    }
    markdownMessage += `*Error Name:* ${escapeMarkdownV2(error.name)}\n`;
    markdownMessage += `*Message:* ${escapeMarkdownV2(error.message)}\n\n`;
    // Code blocks don't need internal escaping for their content, but the surrounding backticks are part of Markdown syntax
    markdownMessage += `*Stack Trace:*\n\`\`\`\n${error.stack || 'No stack trace available'}\n\`\`\`\n`;

    // Telegram API message length limit (MarkdownV2 is 4096)
    const MAX_TELEGRAM_MESSAGE_LENGTH = 4096;
    if (markdownMessage.length > MAX_TELEGRAM_MESSAGE_LENGTH) {
      // Truncate and add suffix. Ensure suffix itself is escaped if it contains Markdown characters.
      const truncationSuffix = "\n... \\(truncated\\)";
      markdownMessage = markdownMessage.substring(0, MAX_TELEGRAM_MESSAGE_LENGTH - truncationSuffix.length) + truncationSuffix;
      this.logger.warn('Error message for Telegram was truncated due to length limits.');
    }

    const url = `${this.TELEGRAM_API_BASE_URL}/bot${this.botToken}/sendMessage`;
    const payload = {
      chat_id: this.chatId,
      text: markdownMessage,
      parse_mode: 'MarkdownV2',
      disable_web_page_preview: true
    };

    try {
      this.logger.debug(`Attempting to send error notification to Telegram Chat ID: ${this.chatId}`);
      await firstValueFrom(
        this.httpService.post(url, payload, {
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      this.logger.log('Successfully sent error notification to Telegram.');
    } catch (apiError) {
      this.logger.error('Failed to send error notification to Telegram.', apiError.response?.data || apiError.message, apiError.stack);
      // Log the payload that failed for debugging, but be careful with sensitive info in logs
      // Only log a small part of the text to avoid overly verbose logs or exposing too much data.
      const textPreview = payload.text.length > 200 ? payload.text.substring(0, 200) + "..." : payload.text;
      this.logger.debug('Failed Telegram payload (text part might be long):', { ...payload, text: textPreview });
    }
  }

  async sendSimpleTextMessage(message: string): Promise<void> {
    if (!this.botToken || !this.chatId) {
      this.logger.warn('Telegram monitoring is disabled due to missing configuration. Cannot send simple text message.');
      return;
    }

    // For simple text, we might not need complex Markdown escaping,
    // but Telegram still has limits and special character considerations.
    // For truly plain text, parse_mode can be omitted.
    // If some basic markdown is desired (e.g. bold, italic), set parse_mode to 'MarkdownV2'
    // and ensure 'message' is appropriately escaped or crafted.
    // Here, we'll send as plain text by omitting parse_mode or setting it to undefined.

    const MAX_TELEGRAM_MESSAGE_LENGTH = 4096; // General limit
    let textToSend = message;

    if (textToSend.length > MAX_TELEGRAM_MESSAGE_LENGTH) {
      const truncationSuffix = "\n... (truncated)";
      textToSend = textToSend.substring(0, MAX_TELEGRAM_MESSAGE_LENGTH - truncationSuffix.length) + truncationSuffix;
      this.logger.warn('Simple text message for Telegram was truncated due to length limits.');
    }

    const url = `${this.TELEGRAM_API_BASE_URL}/bot${this.botToken}/sendMessage`;
    const payload = {
      chat_id: this.chatId,
      text: textToSend,
      // parse_mode: undefined, // Explicitly plain text, or omit
      disable_web_page_preview: true,
    };

    try {
      this.logger.debug(`Attempting to send simple text message to Telegram Chat ID: ${this.chatId}`);
      await firstValueFrom(
        this.httpService.post(url, payload, {
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      this.logger.log('Successfully sent simple text message to Telegram.');
    } catch (apiError) {
      this.logger.error('Failed to send simple text message to Telegram.', apiError.response?.data || apiError.message, apiError.stack);
      const textPreview = payload.text.length > 200 ? payload.text.substring(0, 200) + "..." : payload.text;
      this.logger.debug('Failed Telegram payload (simple text):', { ...payload, text: textPreview });
    }
  }

  async sendDockerHubNotification(dto: DockerHubWebhookDto): Promise<void> {
    const repoName = dto.repository.repo_name;
    const tag = dto.push_data.tag;
    const repoUrl = dto.repository.repo_url;
    const pusher = dto.push_data.pusher;
    const description = dto.repository.description ? `\n描述: ${dto.repository.description}` : '';

    const message = `🐳 Docker Hub 更新通知\n\n仓库: ${repoName}:${tag}\n地址: ${repoUrl}\n推送者: ${pusher}${description}\n\n镜像已成功推送。`;

    this.logger.log(`Sending Docker Hub notification for ${repoName}:${tag}`);
    await this.sendSimpleTextMessage(message);
  }
}