import { Controller, Post, Body, HttpCode, HttpStatus, Logger, ValidationPipe } from '@nestjs/common';
import { MonitorService } from './monitor.service.js'; // 使用 .js 后缀
import { DockerHubWebhookDto } from './dto/docker-hub-webhook.dto.js'; // 使用 .js 后缀
// 移除鉴权导入

@Controller('/monitor')
export class MonitorController {
  private readonly logger = new Logger(MonitorController.name);
  constructor(private readonly monitorService: MonitorService) {}

  @Post('/dockerhub')
  @HttpCode(HttpStatus.OK) // Docker Hub 期望 2xx 响应表示成功接收
  async handleDockerHubWebhook(
    @Body(new ValidationPipe({ transform: true, whitelist: true })) // 保持 DTO 验证
    webhookDto: DockerHubWebhookDto,
  ): Promise<{ message: string }> {
    this.logger.log(`Received Docker Hub webhook for repository: ${webhookDto.repository.repo_name}`);
    try {
      await this.monitorService.sendDockerHubNotification(webhookDto);
      this.logger.log(`Successfully processed Docker Hub webhook for: ${webhookDto.repository.repo_name}`);
      return { message: 'Webhook received and processed successfully.' };
    } catch (error) {
      this.logger.error(`Failed to process Docker Hub webhook for ${webhookDto.repository.repo_name}`, error.stack);
      // 即使处理失败，也返回 200 OK 给 Docker Hub，避免重试风暴
      // 可以在日志中详细记录错误
      return { message: 'Webhook received but failed during processing.' };
    }
  }
}