// src/monitor/monitor.module.ts
import { Module, Global } from '@nestjs/common'; // Added Global
import { HttpModule } from '@nestjs/axios'; // For HttpService
import { MonitorService } from './monitor.service.js';
import { MonitorController } from './monitor.controller.js'; // Import the controller
import { ConfigModule } from '@nestjs/config'; // MonitorService will need ConfigService

@Global() // Make MonitorService globally available if it's used by global filters/interceptors
@Module({
  controllers: [MonitorController], // Add the controller here
  imports: [
    HttpModule, // For sending requests to Telegram API
    ConfigModule, // To access environment variables
  ],
  providers: [MonitorService],
  exports: [MonitorService], // Export MonitorService so it can be injected by global filters
})
export class MonitorModule {}