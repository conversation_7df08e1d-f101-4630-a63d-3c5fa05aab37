import { Modu<PERSON> } from '@nestjs/common';
import { NoteService } from './note.service.js';
import { NoteController } from './note.controller.js';
import { RedisModule } from '../redis/redis.module.js';
import { AuthModule } from '../auth/auth.module.js';
import { MonitorModule } from '../monitor/monitor.module.js';
import { AgentModule } from '../agent/agent.module.js';

@Module({
  imports: [RedisModule, AuthModule, MonitorModule, AgentModule],
  controllers: [NoteController],
  providers: [NoteService],
})
export class NoteModule {}