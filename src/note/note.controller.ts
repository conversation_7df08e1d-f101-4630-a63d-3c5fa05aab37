import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { NoteService } from './note.service.js';
import { CreateNoteDto } from './dto/create-note.dto.js';
import { UpdateNoteDto } from './dto/update-note.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';
import { CurrentUser } from '../auth/decorators/current-user.decorator.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';

@Controller('/note')
@UseGuards(AuthGuard)
export class NoteController {
  constructor(private readonly noteService: NoteService) {}

  /**
   * 保存一个新笔记
   * @param createNoteDto - 创建笔记的数据传输对象
   * @param user - 当前登录用户
   */
  @Post('/save')
  @AuthPermissions(PermissionLevel.PUBLIC)
  save(
    @Body() createNoteDto: CreateNoteDto,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    return this.noteService.save(createNoteDto, user);
  }

  /**
   * 获取笔记的分页列表
   * @param page - 页码
   * @param pageSize - 每页数量
   */
  @Get('/lists')
  @AuthPermissions(PermissionLevel.PUBLIC)
  lists(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('pageSize', new ParseIntPipe({ optional: true })) pageSize?: number,
  ) {
    return this.noteService.lists(page, pageSize);
  }

  /**
   * 修改一个笔记
   * @param id - 笔记ID
   * @param updateNoteDto - 更新笔记的数据传输对象
   * @param user - 当前登录用户
   */
  @Patch('/modify/:id')
  @AuthPermissions(PermissionLevel.PUBLIC)
  modify(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateNoteDto: UpdateNoteDto,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    return this.noteService.modify(id, updateNoteDto, user);
  }

  /**
   * 删除一个笔记
   * @param id - 笔记ID
   * @param user - 当前登录用户
   */
  @Delete('/remove/:id')
  @AuthPermissions(PermissionLevel.PUBLIC)
  remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: AuthenticatedUser,
  ) {
    return this.noteService.removeForUser(id, user);
  }

  /**
   * 点赞一个笔记
   * @param id - 笔记ID
   */
  @Post('/like/:id')
  @AuthPermissions(PermissionLevel.PUBLIC)
  like(@Param('id', ParseIntPipe) id: number) {
    return this.noteService.like(id);
  }

  /**
   * 重建笔记索引
   */
  @Post('/index')
  @AuthPermissions(PermissionLevel.READ_WRITE)
  index() {
    return this.noteService.index();
  }
}