import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { BaseDataService } from '../common/services/base-data.service.js';
import { NoteEntity } from './entities/note.entity.js';
import { RedisService } from '../redis/redis.service.js';
import { CreateNoteDto } from './dto/create-note.dto.js';
import { UpdateNoteDto } from './dto/update-note.dto.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';
import { TaskLogic } from '../common/decorators/task-logic.decorator.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { TaskParam } from '../common/decorators/task-param.decorator.js';
import { AgentService } from '../agent/agent.service.js';
import { MonitorService } from '../monitor/monitor.service.js';

@Injectable()
export class NoteService extends BaseDataService<NoteEntity> {
  private readonly logger = new Logger(NoteService.name);

  constructor(
    protected readonly redisService: RedisService,
    protected readonly agentService: AgentService,
    protected readonly monitorService: MonitorService,
  ) {
    super(NoteEntity, redisService);
  }

  protected getKeyPrefix(): string {
    return this.entityName; // 使用默认的实体名称作为前缀
  }

  protected async generateId(entity: Partial<NoteEntity>): Promise<string> {
    // 为 Note 生成数字 ID，然后转换为字符串
    const numericId = await this.redisService.incr(`${this.entityName}:id`);
    return numericId.toString();
  }

  /**
   * 创建一个新的笔记
   * @param createNoteDto - 创建笔记的数据
   * @param user - 当前登录用户
   * @returns 创建的笔记实体
   */
  async save(
    createNoteDto: CreateNoteDto,
    user: AuthenticatedUser,
  ): Promise<NoteEntity> {
    const note = new NoteEntity();
    Object.assign(note, createNoteDto);
    note.userId = user?.id?.toString();
    note.platform = user?.provider;
    note.username = createNoteDto.username || user?.username || '匿名';
    note.likes = 0;
    this.logger.log(
      `User ${user?.username} (${user?.id}) creating a new note with username ${note.username}.`,
    );
    return this.saveOrModify(note);
  }

  /**
   * 修改一个笔记
   * @param id - 笔记ID
   * @param updateNoteDto - 更新笔记的数据
   * @param user - 当前登录用户
   * @returns 更新后的笔记实体
   */
  async modify(
    id: number,
    updateNoteDto: UpdateNoteDto,
    user: AuthenticatedUser,
  ): Promise<NoteEntity> {
    const note = await this.get(id.toString());
    if (!note) {
      this.logger.warn(`Note with id ${id} not found for modification attempt.`);
      throw new NotFoundException('Note not found');
    }
    // 如果笔记没有关联用户，则允许任何用户修改
    if (note.userId) {
      if(!user || note.userId !== user.id.toString() || note.platform !== user.provider) {
        this.logger.warn(
          `User ${user.username} (${user.id}) attempted to modify note ${id} without permission.`,
        );
        throw new UnauthorizedException('You can only modify your own notes.');
      }
    }
    Object.assign(note, updateNoteDto);
    this.logger.log(`User ${user?.username} (${user?.id}) modified note ${id}.`);
    return this.saveOrModify(note);
  }

  /**
   * 为指定用户删除一个笔记
   * @param id - 笔记ID
   * @param user - 当前登录用户
   */
  async removeForUser(id: number, user: AuthenticatedUser): Promise<void> {
    const note = await this.get(id.toString());
    if (!note) {
      this.logger.warn(`Note with id ${id} not found for removal attempt.`);
      throw new NotFoundException('Note not found');
    }

    // 如果笔记没有关联用户，则允许任何用户删除
    if (note.userId) {
      if (!user || note.userId !== user.id.toString() || note.platform !== user.provider) {
        this.logger.warn(
          `User ${user.username} (${user.id}) attempted to remove note ${id} without permission.`,
        );
        throw new UnauthorizedException('You can only remove your own notes.');
      }
    }
    this.logger.log(`User ${user?.username} (${user?.id}) removed note ${id}.`);
    return super.remove(id.toString());
  }

  /**
   * 点赞一个笔记
   * @param id - 笔记ID
   * @returns 更新后的笔记实体
   */
  async like(id: number): Promise<NoteEntity> {
    const note = await this.get(id.toString());
    if (!note) {
      this.logger.warn(`Note with id ${id} not found for like attempt.`);
      throw new NotFoundException('Note not found');
    }
    note.likes += 1;
    this.logger.log(`Note ${id} liked. New like count: ${note.likes}.`);
    return this.saveOrModify(note);
  }

  /**
   * 保存linuxdo树洞笔记
   * @param content - 笔记内容
   * @param tag - 笔记标签
   * @param username - 用户名
   * @param userId - 用户ID
   * @returns 保存的笔记实体
   */
  @TaskLogic(TaskLogicEnum.SAVE_LINUX_DO_NOTE)
  async saveLinuxdoNote(
    @TaskParam("content") content: string,
    @TaskParam("tags")  tags: string[],
    @TaskParam("username") username: string,
    @TaskParam("userId") userId: string,
  ): Promise<NoteEntity> { 

    if(!content) {
      throw new BadRequestException("为获取到笔记内容")
    }
    const note = new NoteEntity();
    note.userId = userId;
    note.platform = 'linuxdo';
    note.username = username || '匿名';
    note.content = content;
    note.tags = tags || [];
    const styles = ['#a2e1d4', '#acf6ef', '#cbf5fb', '#bdf3d4', '#e6e2c3', '#e3c887', '#fad8be', '#fbb8ac','#fe6673', '#d7d4f0'];
    note.style = styles[Math.floor(Math.random() * styles.length)];
    note.likes = 0;
    this.logger.log(
      `add linuxdo node ${username} ${content}.`,
    );
    return this.saveOrModify(note);
   }



  @TaskLogic(TaskLogicEnum.SAVE_LINUX_DO_NOTE_BATCH)
  async saveLinuxdoNoteBatch(@TaskParam("noteTopicIds") topicIds:number[], @TaskParam("topicProcessAgentName") topicProcessAgentName: string) {
    this.logger.debug(`AI agent picked ${topicIds && topicIds.length || 0} topics`);

    const fetchTopicIds:number[] = [];
    for (const id of topicIds) {
      const note = await this.get(id.toString());
      if (note) {
        this.logger.warn(`Note with id ${id} found.`);
        continue; // 如果已经存在，则跳过
      }
      fetchTopicIds.push(id);
    }

    this.logger.debug(`Need to Save ${fetchTopicIds.length} topics`);
    const results: any[] = [];
    for (const id of fetchTopicIds) {
      try {
        this.logger.debug(`Fetching topic ${id}`);
        const t = await this.fetchAndSaveTopic(id, topicProcessAgentName);
        results.push(t);
        this.logger.debug(`Successfully fetched topic ${id}`);
      } catch (e) {
        this.logger.error(`Error fetching topic ${id}: ${e.message}`, e.stack);
        // 记录错误后继续
      }
      // 等 120 秒，避免请求过于频繁
      if (fetchTopicIds.length - results.length > 0) {
        this.logger.debug('Waiting 120 seconds before next fetch...');
        await new Promise((r) => setTimeout(r, 120_000));
      }
    }

    this.logger.debug(`Successfully fetched ${results.length} topics`);
    this.monitorService.sendSimpleTextMessage(`linux fetch topic: Successfully fetched ${results.length} topics`)
  
  }

    async fetchAndSaveTopic(topicId: number, topicProcessAgentName: string = 'linuxdo-topic-note-process-agent') {
       await this.agentService.runFullAgentProcess(topicProcessAgentName, { topicId })
    }
  
}