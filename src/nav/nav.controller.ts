import {
  Controller,
  Get,
  Query,
  Req,
  Delete,
  Param,
  UseGuards
} from '@nestjs/common';
import { NavService } from './nav.service.js';
import { AddNavDto } from './dto/add-nav.dto.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';
import { CurrentUser } from '../auth/decorators/current-user.decorator.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { AuthGuard } from '../common/guards/auth.guard.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';

@Controller('/nav')
export class NavController {
  constructor(private readonly nav: NavService) {}

  /**
   * 
   * @param user 
   * @returns 
   */
  @Get('/items')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.PUBLIC)
  items(@CurrentUser() user: AuthenticatedUser) {
    return this.nav.getNavItems(user);
  }

 
  /**
   * 
   * @param query 
   * @returns 
   */
  @Get('/add')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.AUTH_USER)
  addNav(@Query() query: AddNavDto, @CurrentUser() user: AuthenticatedUser) {
    return this.nav.addNav(query.topicId);
  }

  /**
   * 
   * @param topicId 
   * @returns 
   */
  @Delete('/items/:topicId')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.AUTH_USER)
  deleteNav(@Param('topicId') topicId: string) {
    return this.nav.deleteNavItem(topicId);
  }

  /**
   * 批量
   * @returns 
   */
  @Get('/fetch')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.AUTH_USER)
  fetchNew() {
    return this.nav.fetchNewTopics();
  }
}
