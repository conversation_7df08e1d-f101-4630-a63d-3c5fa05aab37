import { Module } from '@nestjs/common';
import { NavController } from './nav.controller.js';
import { NavService } from './nav.service.js';
import { RedisModule } from '../redis/redis.module.js';
import { AgentModule } from '../agent/agent.module.js'; // 导入全局 AgentModule
import { FetchModule } from '../fetch/fetch.module.js'; // 导入全局 FetchModeule
import { AuthModule } from '../auth/auth.module.js'; // 导入全局 AuthModule
import { MonitorModule } from '../monitor/monitor.module.js';

@Module({
  imports: [FetchModule, RedisModule, AgentModule, AuthModule, MonitorModule], // 导入 AgentModule
  controllers: [NavController],
  providers: [NavService],
})
export class NavModule {}
