import {
  BadRequestException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { RedisService } from '../redis/redis.service.js';
import { AgentService } from '../agent/index.js';
import { AuthenticatedUser } from '../auth/dto/authenticated-user.dto.js';
import { FetchService } from '../fetch/fetch.service.js';
import { LockService } from '../redis/lock.service.js';
import { RedisLock } from '../common/decorators/redis-lock.decorator.js';
import { ConfigService } from '@nestjs/config';
import { TaskLogic } from '../common/decorators/task-logic.decorator.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { MonitorService } from '../monitor/monitor.service.js';
import { TaskParam } from '../common/decorators/task-param.decorator.js';


@Injectable()
export class NavService {

  private readonly logger = new Logger(NavService.name);

   private readonly LINUXDO_API_KEY: string;

  constructor(
    private readonly fetchService: FetchService,
    private readonly redisService: RedisService,
    private readonly agent: AgentService,
    private readonly lockService: LockService,
    private readonly configService: ConfigService,
    private readonly monitorService: MonitorService
  ) {
    this.LINUXDO_API_KEY = this.configService.getOrThrow<string>('LINUXDO_API_KEY');
  }


  async userTrustLevel(authUser: AuthenticatedUser) {
    return authUser && authUser.trust_level || 0
  }


  async getNavItems(authUser: AuthenticatedUser) {
    const trust = await this.userTrustLevel(authUser);
    const raw = await this.redisService.get('nav_items');
    const all = raw ? JSON.parse(raw) : [];
    return all.filter((i) => (i.trustLevel ?? 0) <= trust);
  }

  /**
   * /nav/add?topicId=xxx
   */
  async addNav(topicId: string) {

    if (!topicId) {
      throw new BadRequestException('topicId is required');
    }
    return this.fetchAndSaveTopic(Number(topicId));
  }

  @RedisLock({ keyPattern: "linuxDoNavLock" })
  async deleteNavItem(topicId: string) {

    const raw = await this.redisService.get('nav_items');
    const list = raw ? JSON.parse(raw) : [];

    const idx = list.findIndex((i) => i.url.split('/').pop() === String(topicId));
    if (idx === -1) {
      return { success: false, message: 'Item not found' };
    }

    list.splice(idx, 1);
    await this.redisService.set('nav_items', JSON.stringify(list));
    return { success: true, message: 'Item deleted successfully' };
  }


  @RedisLock({ keyPattern: "linuxDoNavLock" })
  async fetchAndSaveTopic(topicId: number, topicProcessAgentName: string = 'linuxdo-topic-process-agent') {
     await this.agent.runFullAgentProcess(topicProcessAgentName, { topicId })
  }



  @TaskLogic(TaskLogicEnum.FETCH_LINUX_DO_TOPIC)
  async fetchTopic(@TaskParam("topicId") topicId: number) {
    try {
      this.logger.debug(`Fetching topic ${topicId} details`);
      const headers = { 'User-Api-Key': this.LINUXDO_API_KEY };
      const url = `https://linux.do/t/topic/${topicId}.json`;

      const data = await firstValueFrom(this.fetchService.get<any>(url, { headers }))
        .then((r) => JSON.parse(r.data))
        .catch((error) => {
          this.logger.error(`HTTP error fetching topic ${topicId}: ${error.message}`, error.stack);
          throw new Error(`Failed to fetch topic: ${error.message}`);
        });

      if (!data || !data.post_stream) {
        this.logger.error(`Invalid response format for topic ${topicId} ${data}`);
        throw new Error('Invalid topic response format');
      }

      const post = data.post_stream?.posts?.[0];
      if (!post) {
        this.logger.error(`No posts found for topic ${topicId}`);
        throw new Error('Invalid topic json - no posts found');
      }

      post.title = data.fancy_title ?? data.title;

      post.tags = data.tags || [];

      this.logger.debug(`Processing topic ${topicId} with AI agent`);

      return post;
    } catch (error) {
      this.logger.error(`Error in fetchTopic for ID ${topicId}: ${error.message}`, error.stack);
      throw error; 
    }
  }



  @TaskLogic(TaskLogicEnum.UPDATE_LINUX_DO_NAV_ITEM)
  async renewLinuxDoNavItem(@TaskParam("payload") payload:string) {
     const summary = JSON.parse(payload);

      const raw = await this.redisService.get('nav_items');
      const list = raw ? JSON.parse(raw) : [];

      const idx = list.findIndex((i) => i.id === summary.id);
      if (idx === -1) {
        this.logger.debug(`Adding new topic ${summary.id} to nav_items`);
        list.push(summary);
      } else {
        this.logger.debug(`Updating existing topic ${summary.id} in nav_items`);
        list[idx] = summary;
      }

      await this.redisService.set('nav_items', JSON.stringify(list));
      this.logger.debug(`Successfully processed topic ${summary.id}`);
      return summary;
  }

  @TaskLogic(TaskLogicEnum.FETCH_LINUX_DO_NEW_TOPIC_LIST)
  async fetchNewTopics() {

    const headers = { 'User-Api-Key': this.LINUXDO_API_KEY };

    this.logger.debug('Fetching new topics from linux.do');
    const data = await firstValueFrom(
      this.fetchService.get<any>('https://linux.do/new.json', { headers })
    ).then((r) => JSON.parse(r.data))
    .catch((error) => {
      this.logger.error(`HTTP error fetching new.json: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch new.json: ${error.message}`);
    });

  
    if (!data || !data.topic_list) {
      this.logger.error(`Invalid response format from linux.do/new.json resp: ${data}`);
      throw new BadRequestException("获取linuxdo news.json 失败")
    }

    const topics = data.topic_list?.topics ?? [];
    this.logger.debug(`Found ${topics.length} topics`);

    const brief = topics.map(({ id, title, tags }) => ({ id, title, tags }));

    this.logger.debug('Calling AI agent to analyze topics');

    return brief;
  }

    

  @TaskLogic(TaskLogicEnum.UPDATE_LINUX_DO_NAV_ITEM_BATCH)
  async updateNavItemBatch(@TaskParam("navTopicIds") pickedIds: number[], @TaskParam("topicProcessAgentName") topicProcessAgentName: string) {
    
    if (!pickedIds || !pickedIds.length) {
      this.logger.warn('No topics picked for processing');
      this.monitorService.sendSimpleTextMessage("linux fetch topic: No topics picked for processing")
      return { success: false, message: 'No topics picked for processing' };
    }
  
    this.logger.debug(`AI agent picked ${pickedIds.length} topics`);

    const raw = await this.redisService.get('nav_items');
    const list = raw ? JSON.parse(raw) : [];

    const needFetch = pickedIds.filter((id) => !list.some((i) => i.id === id));
    if (!needFetch.length) {
      this.monitorService.sendSimpleTextMessage("linux fetch topic: No new topics to process")
      this.logger.debug('No new topics to process');
      return ;
    }

    this.logger.debug(`Need to Save ${needFetch.length} topics`);
    const results: any[] = [];
    for (const id of needFetch) {
      try {
        this.logger.debug(`Fetching topic ${id}`);
        const t = await this.fetchAndSaveTopic(id, topicProcessAgentName);
        results.push(t);
        this.logger.debug(`Successfully fetched topic ${id}`);
      } catch (e) {
        this.logger.error(`Error fetching topic ${id}: ${e.message}`, e.stack);
        // 记录错误后继续
      }
      // 等 120 秒，避免请求过于频繁
      if (needFetch.length - results.length > 0) {
        this.logger.debug('Waiting 120 seconds before next fetch...');
        await new Promise((r) => setTimeout(r, 120_000));
      }
    }

    this.logger.debug(`Successfully fetched ${results.length} topics`);
    this.monitorService.sendSimpleTextMessage(`linux fetch topic: Successfully fetched ${results.length} topics`)
    return { success: true, data: results };
  
  }
}
