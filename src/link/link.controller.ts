import { Body, Controller, Get, Post } from '@nestjs/common';
import { LinkService } from './link.service.js';
import { AddLinkDto } from './add-link.dto.js';

@Controller('/link')
export class LinkController {
  constructor(private readonly linkService: LinkService) {}

  @Get('/lists')
  lists() {
    return this.linkService.lists();
  }

  @Post('/add')
  add(@Body() dto: AddLinkDto) {
    return this.linkService.addLink(dto);
  }
}
