import { Module } from '@nestjs/common';
import { HttpModule } from '../common/http.module.js';
import { LinkController } from './link.controller.js';
import { LinkService } from './link.service.js';

/**
 * 假设 RedisService 在 RedisModule 中 export 出来
 * 若你的 RedisService 位于其它模块，请按实际路径引入
 */
import { RedisModule } from '../redis/redis.module.js';

@Module({
  imports: [RedisModule, HttpModule],
  controllers: [LinkController],
  providers: [LinkService],
})
export class LinkModule {}