import {
  ConflictException,
  Injectable,
  HttpException,
  HttpStatus 
} from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../redis/redis.service.js';
import { LockService } from '../redis/lock.service.js';
import { AddLinkDto } from './add-link.dto.js';
import { Link } from './link.type.js';
import { RedisLock } from '../common/decorators/redis-lock.decorator.js';

@Injectable()
export class LinkService {

  constructor(
    private readonly redis: RedisService,
    private readonly lockService: LockService,
    private readonly http: HttpService,
  ) {}

  /**
   * 
   * @returns 链接列表
   */
  async lists(): Promise<Link[]> {
    const raw = await this.redis.get('linkList');
    return raw ? JSON.parse(raw) : [];
  }

  @RedisLock({ keyPattern : "addLinkLock"})
  async addLink(dto: AddLinkDto): Promise<{ message: string }> {
    // 2⃣ 解析 URL、查重
    const linkUrl = new URL(dto.link);
    const raw = await this.redis.get('linkList');
    const list: any[] = raw ? JSON.parse(raw) : [];

    if (list.some((l) => l.host === linkUrl.host)) {
      throw new ConflictException('请勿重复添加网站');
    }

    // 3⃣ 可达性检查（HEAD 比 GET 快）
    await firstValueFrom(
      this.http.head(dto.link, { timeout: 5000, validateStatus: () => true }),
    );

    // 4⃣ 写回 Redis
    list.push({ ...dto, host: linkUrl.host });
    await this.redis.set('linkList', JSON.stringify(list));

    return { message: '网站添加成功 <a href="/">刷新</a>' };
  
  }
}
