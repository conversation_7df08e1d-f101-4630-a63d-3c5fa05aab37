// agent/tools.ts
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

import OpenAI from 'openai';

// Define placeholder types for MCP SDK components if not found
export interface MCPInputSchemaSDK extends Record<string, any> {}

export interface MCPToolSDK {
  name: string;
  description?: string;
  inputSchema: MCPInputSchemaSDK;
  // Add other properties if known from SDK's actual Tool definition
}

export interface MCPOutputSDK extends Record<string, any> {
  // Define more specific structure if known, e.g., based on client.callTool() response
  content?: Array<{ type: string; text?: string; [key: string]: any }>; // Example structure
  isError?: boolean;
}


/**
 * Configuration options for the Tools class.
 */
export interface ToolsConfigOptions {
  url?: string; // For SSE transport
  command?: string; // For Stdio transport
  args?: string[]; // For Stdio transport
  env?: Record<string, string | undefined>; // For Stdio transport
  shell?: boolean | string; // For Stdio transport
}

/**
 * Configuration for initializing the Tools client.
 */
export interface ToolsConfig { // Added export
  name?: string;
  version?: string;
  transport?: 'stdio' | 'sse';
  specifyTools?: string[] | false;
  options?: ToolsConfigOptions;
}


/**
 * A client wrapper around Model Context Protocol.
 * Provides methods to list and call tools in a format compatible with OpenAI API.
 */
class Tools {
  private name: string;
  private version: string;
  private transportType: 'stdio' | 'sse';
  private specifyTools: string[] | false;
  private options: ToolsConfigOptions;
  private client: Client | null;
  private transport: StdioClientTransport | SSEClientTransport | null;
  private toolsCache: OpenAI.ChatCompletionTool[] | null;

  /**
   * Create a new Tools instance and automatically connect to MCP server.
   * @param {ToolsConfig} config - Configuration options.
   */
  constructor(config: ToolsConfig) {
    if (!config) {
      throw new Error('Tools configuration is required');
    }

    this.name = config.name || 'mcp-tools-client';
    this.version = config.version || '1.0.0';
    this.transportType = config.transport || 'stdio';
    this.specifyTools = config.specifyTools || false; // Corrected typo from secifyTools
    this.options = config.options || {};
    this.client = null;
    this.transport = null;
    this.toolsCache = null;

    // Automatically initialize the client
    this.initialize().then(() => {
      console.log(`${this.name} v${this.version} connected using ${this.transportType} transport`);
    }).catch((error: any) => {
      console.error('Failed to initialize client:', error);
    });
  }

  /**
   * Initialize the MCP client.
   * @returns {Promise<void>}
   */
  async initialize(): Promise<void> {
    this.client = new Client({
      name: this.name,
      version: this.version,
    });

    this.transport = this.createTransport();

    if (this.transport) {
      this.transport.onerror = (error: Error) => {
        console.error('Transport error:', error);
      };
      if (this.client) {
        await this.client.connect(this.transport);
      } else {
        // This case should ideally not be reached if constructor logic is sound.
        console.error('Client is null during connect attempt in initialize');
        throw new Error('Client initialization failed before connect.');
      }
    } else {
        console.error('Transport is null after createTransport() call');
        throw new Error('Transport creation failed.');
    }
  }

  /**
   * List available tools from the MCP server.
   * @returns {Promise<OpenAI.ChatCompletionTool[]>} Array of tools in OpenAI format.
   */
  async listTools(): Promise<OpenAI.ChatCompletionTool[]> {
    try {
      if (!this.client || !this.transport) { // Ensure transport is also initialized
        await this.initialize();
      }

      if (this.toolsCache) {
        return this.toolsCache;
      }

      // Client should be initialized by this.initialize() if it was null
      if (!this.client) {
          console.error('Client is null before listing tools despite initialization attempt.');
          throw new Error('Client not initialized before listing tools.');
      }

      const result = await this.client.listTools();
      const tools: MCPToolSDK[] = result.tools || [];

      const openaiTools: OpenAI.ChatCompletionTool[] = tools
        .filter((tool: MCPToolSDK) => {
          return this.specifyTools ? this.specifyTools.includes(tool.name) : true
        })
        .map((tool: MCPToolSDK) => ({
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.inputSchema
          },
        }));

      this.toolsCache = openaiTools;
     
      return openaiTools;
    } catch (error: any) {
      console.error('Error listing tools:', error);
      return [];
    }
  }

  /**
   * Call a tool on the MCP server.
   * @param {string} name - Tool name.
   * @param {Record<string, any>} args - Tool arguments.
   * @returns {Promise<ToolOutput | { content: { type: 'text'; text: string }[]; isError: true }>} Tool result.
   */
  async callTool(name: string, args: Record<string, any>): Promise<MCPOutputSDK | { content: { type: 'text'; text: string }[]; isError: true }> {
    try {
      if (!this.client || !this.transport) { // Ensure transport is also initialized
        await this.initialize();
      }

      // Client should be initialized by this.initialize() if it was null
      if (!this.client) {
          console.error('Client is null before calling tool despite initialization attempt.');
          throw new Error('Client not initialized before calling tool.');
      }

      const result = await this.client.callTool({
        name: name,
        arguments: args,
      });
      return result;
    } catch (error: any) {
      console.error(`Error calling tool ${name}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        content: [{ type: 'text', text: `Error: ${errorMessage}` }],
        isError: true,
      };
    }
  }

  /**
   * Create the appropriate transport based on configuration.
   * @returns {StdioClientTransport | SSEClientTransport} The created transport.
   */
  private createTransport(): StdioClientTransport | SSEClientTransport {
    if (this.transportType === 'sse') {
      if (!this.options.url) {
        throw new Error('SSE transport requires a URL');
      }
      return new SSEClientTransport(new URL(this.options.url));
    } else { // stdio
      if (!this.options.command) {
        throw new Error('Stdio transport requires a command');
      }
      console.log(`Creating stdio transport with command: ${this.options.command} and args:`, this.options.args);
      // Create a new options object for StdioClientTransport to ensure correct typing,
      // especially for 'command' which is now confirmed to be a string.
      const stdioOptions = {
        command: this.options.command, // command is string here
        args: this.options.args,
        env: this.options.env ? Object.entries(this.options.env).reduce((acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = value;
          }
          return acc;
        }, {} as Record<string, string>) : undefined,
        shell: this.options.shell,
        // Include any other properties from ToolsConfigOptions that StdioClientTransport might expect
        // url is not for stdio, so it's fine if it's part of ...this.options but not used.
        // However, to be precise, only pass relevant properties.
      };
      return new StdioClientTransport(stdioOptions);
    }
  }

  /**
   * Check if a tool with the given name exists.
   * @param {string} name - Tool name to check.
   * @returns {Promise<boolean>} True if the tool exists, false otherwise.
   */
  async hasTool(name: string): Promise<boolean> {
    try {
      const tools = await this.listTools();
      return tools.some(
        (tool: OpenAI.ChatCompletionTool) => tool.function && tool.function.name === name
      );
    } catch (error: any) {
      console.error(`Error checking for tool ${name}:`, error);
      return false;
    }
  }

  /**
   * Get the client instance.
   * @returns {Client | null} The client instance.
   */
  getClient(): Client | null {
    return this.client;
  }

  /**
   * Stop the server and close the transport.
   * @returns {Promise<void>}
   */
  async stop(): Promise<void> {
    if (this.transport) {
      await this.transport.close();
      this.transport = null;
    }
    console.log(`${this.name} stopped`);
  }
}

export default Tools;
