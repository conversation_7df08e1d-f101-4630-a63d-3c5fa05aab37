import Agent, { AgentOptions} from './agent.js';
import Tools, { ToolsConfig } from './tools.js'; // Assuming agent/tools.ts will export this
import Model, { ModelOptions } from './model.js';

interface DefaultConfig {
  temperature: number;
  maxTokens: number;
  prompt: string;
}

interface ParserOptions {
  defaultConfig?: Partial<DefaultConfig>;
}



interface AgentSpecificCallbacks {
  afterAgentCallback?: (replyContent: string) => Promise<void>;
  beforeAgentCallback?: (systemPrompt: string) => Promise<string>;
}

// For top-level/global callbacks
interface GlobalCallbacks {
  afterAgentCallback?: (replyContent: string) => Promise<void>;
  beforeAgentCallback?: (systemPrompt: string) => Promise<string>;
}

// For callbacks nested under an agent's name
interface PerAgentCallbacks {
  [agentName: string]: AgentSpecificCallbacks | undefined;
}

// The main Callbacks type for parameters
export type Callbacks = GlobalCallbacks & PerAgentCallbacks;


export interface AgentSchema {
  name: string;
  model: ModelOptions;
  tools?: ToolsConfig[];
  subAgents?: AgentSchema[];
  // Schema can also define callbacks, these would be top-level for *this* agent if not overridden by Callbacks param
  afterAgentCallback?: (replyContent: string) => Promise<void>;
  beforeAgentCallback?: (systemPrompt: string) => Promise<string>;
  [key: string]: any;
}

class Parser {
  private defaultConfig: DefaultConfig;

  constructor(options: ParserOptions = {}) {
    this.defaultConfig = {
      temperature: 0.7,
      maxTokens: 4096,
      prompt: 'You are a helpful assistant.',
      ...(options.defaultConfig || {}),
    };
  }

  createModel(modelConfig: ModelOptions): Model {
    if (!modelConfig) {
      throw new Error('Model configuration is required');
    }
    // Spread all properties from modelConfig.
    // Ensure 'model' (for Model class constructor) is derived from 'name' or 'model' in config, with a default.
    // apiKey is required in ModelConfig, so it will be passed via the spread.
    return new Model(modelConfig);
  }

  validateSchema(schema: AgentSchema): void {
    if (!schema) {
      throw new Error('Schema is required');
    }
    if (!schema.model) {
      throw new Error('Schema must include a model configuration');
    }
  }

  createTools(toolsConfig?: ToolsConfig[]): Tools[] {
    if (!toolsConfig || !Array.isArray(toolsConfig)) {
      return [];
    }
    return toolsConfig.map(config => {
      try {
        return new Tools(config);
      } catch (error) {
        console.error(`Failed to create tool from config:`, config, error);
        return null;
      }
    }).filter((tool): tool is Tools => tool !== null);
  }

  createAgentCallbacks(agentName: string, callbacks: Callbacks = {}): AgentSpecificCallbacks {
    let afterAgentCallback: ((replyContent: string) => Promise<void>) | undefined;
    let beforeAgentCallback: ((systemPrompt: string) => Promise<string>) | undefined;

    // Check for agent-specific callback first
    const agentSpecificCbs = callbacks[agentName];
    if (agentSpecificCbs && typeof agentSpecificCbs.afterAgentCallback === 'function') {
      afterAgentCallback = agentSpecificCbs.afterAgentCallback;
    } else if (typeof callbacks.afterAgentCallback === 'function') { // Fallback to global callback
      afterAgentCallback = callbacks.afterAgentCallback;
    }

    if (agentSpecificCbs && typeof agentSpecificCbs.beforeAgentCallback === 'function') {
      beforeAgentCallback = agentSpecificCbs.beforeAgentCallback;
    } else if (typeof callbacks.beforeAgentCallback === 'function') { // Fallback to global callback
      beforeAgentCallback = callbacks.beforeAgentCallback;
    }

    return {
      afterAgentCallback,
      beforeAgentCallback
    };
  }

  createSubAgents(subAgentsSchema?: AgentSchema[], callbacks: Callbacks = {}): Agent[] {
    if (!subAgentsSchema || !Array.isArray(subAgentsSchema)) {
      return [];
    }
    return subAgentsSchema.map(subAgentSchema =>
      this.createAgent(subAgentSchema, callbacks)
    );
  }

  createAgent(schema: AgentSchema, callbacks: Callbacks = {}): Agent {
    this.validateSchema(schema);

    const agentName = schema.name || `agent-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    const modelInstance = this.createModel(schema.model); // Renamed to avoid conflict with schema.model
    const toolsInstances = this.createTools(schema.tools); // Renamed
    const subAgentInstances = this.createSubAgents(schema.subAgents, callbacks); // Renamed

    const {
      model: _modelSchema, // Destructure to exclude from agentConfig
      tools: _toolsSchema,
      subAgents: _subAgentsSchema,
      afterAgentCallback: schemaAfterAgentCallback, // Capture schema-defined callbacks
      beforeAgentCallback: schemaBeforeAgentCallback,
      ...agentConfig // Remaining properties from schema
    } = schema;

    const agentSpecificCallbacks = this.createAgentCallbacks(agentName, callbacks);

    // Determine final callbacks: agent-specific from Callbacks param take precedence,
    // then schema-defined callbacks, then nothing.
    const finalAfterAgentCallback = agentSpecificCallbacks.afterAgentCallback || schemaAfterAgentCallback;
    const findBeforeAgentCallback = agentSpecificCallbacks.beforeAgentCallback || schemaBeforeAgentCallback;

    // TODO: Resolve type incompatibility between Tools[] (from tools.ts) and AgentTool[] (interface from agent.ts)
    // Tools class has async methods (listTools, hasTool) while AgentTool interface expects sync methods.
    // This can lead to runtime errors in Agent class if it calls these methods synchronously.
    // For now, using type assertion to satisfy AgentOptions.
    const config: AgentOptions = {
      ...this.defaultConfig, // Provides temperature, maxTokens, prompt
      ...agentConfig,        // Can override defaults, and provide other AgentOptions fields
      name: agentName,
      model: modelInstance,   // Compatible: Model class instance matches Model interface
      tools: toolsInstances, // Type assertion due to incompatibility
      subAgents: subAgentInstances, // Compatible
      // prompt is ensured by defaultConfig or agentConfig
      afterAgentCallback: finalAfterAgentCallback,
      beforeAgentCallback: findBeforeAgentCallback,
    };

    return new Agent(config);
  }

  createAgentFromString(schemaString: string, callbacks: Callbacks = {}): Agent {
    try {
      const schemaFromJson: AgentSchema = JSON.parse(schemaString);
      return this.createAgent(schemaFromJson, callbacks);
    } catch (error: any) {
      throw new Error(`Failed to parse schema string: ${error.message}`);
    }
  }
}

export default Parser;