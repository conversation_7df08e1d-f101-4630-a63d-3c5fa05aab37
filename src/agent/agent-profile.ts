import { AgentSchema } from "./parser.js";

// Define AgentProfile for persistence
export interface TaskLogicHandler {
  logicId: string; // Corresponds to TaskLogicEnum, store as string
  params?: Record<string, any>;
}

export interface AgentSchemaSerializable extends AgentSchema {
  beforeAgentCallbackHandler?: TaskLogicHandler; // Corrected typo
  afterAgentCallbackHandler?: TaskLogicHandler; // Corrected typo
}


export interface AgentProfile {
  name: string;
  description?: string;
  configSchema: AgentSchemaSerializable;
  graphSchema: any;
  messageProviderHandler: TaskLogicHandler; //消息输入 - Corrected typo
  createdAt: Date;
  updatedAt: Date;
}