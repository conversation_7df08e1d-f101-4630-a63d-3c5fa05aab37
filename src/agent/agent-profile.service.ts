import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { RedisService } from '../redis/redis.service.js';
import { CreateAgentDto } from './dto/create-agent.dto.js';
import { UpdateAgentDto } from './dto/update-agent.dto.js';
import { AgentProfile } from './agent-profile.js';

const AGENT_HASH_KEY = 'agents';
const AGENT_PROFILE_NAME_LIST_KEY = 'agent-profile-names';

@Injectable()
export class AgentProfileService {
  constructor(private readonly redisService: RedisService) {}

  async createAgent(createAgentDto: CreateAgentDto): Promise<AgentProfile> {
    const { name } = createAgentDto;
    const existingAgent = await this.redisService.hget(AGENT_HASH_KEY, name);
    if (existingAgent) {
      throw new ConflictException(`Agent with name '${name}' already exists.`);
    }

    const newAgent: AgentProfile = {
      ...createAgentDto,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await this.redisService.hset(AGENT_HASH_KEY, name, JSON.stringify(newAgent));
    await this.redisService.rpush(AGENT_PROFILE_NAME_LIST_KEY, name); // Add name to list
    return newAgent;
  }

  async findAllAgents(): Promise<AgentProfile[]> {
    // 1. 首先获取哈希表中的所有代理数据
    const allAgentsData = await this.redisService.hgetall(AGENT_HASH_KEY);
    if (!allAgentsData || Object.keys(allAgentsData).length === 0) {
      return []; // 没有代理数据，返回空数组
    }

    // 2. 获取排序列表
    const agentNames = await this.redisService.lrange(AGENT_PROFILE_NAME_LIST_KEY, 0, -1);
    
    // 3. 如果排序列表存在且不为空，使用它来排序
    if (agentNames && agentNames.length > 0) {
      const orderedAgentProfiles: AgentProfile[] = [];
      const processedNames = new Set<string>();
      
      // 按列表顺序添加代理
      for (const name of agentNames) {
        const agentString = allAgentsData[name];
        if (agentString) {
          orderedAgentProfiles.push(JSON.parse(agentString as string) as AgentProfile);
          processedNames.add(name);
        }
      }
      
      // 添加哈希表中存在但不在列表中的代理
      for (const name in allAgentsData) {
        if (!processedNames.has(name)) {
          orderedAgentProfiles.push(JSON.parse(allAgentsData[name] as string) as AgentProfile);
        }
      }
      
      return orderedAgentProfiles;
    }
    
    // 4. 如果排序列表不存在或为空，直接返回所有代理数据
    return Object.values(allAgentsData).map(
      agentString => JSON.parse(agentString as string) as AgentProfile
    );
  }

  async findAgentByName(name: string): Promise<AgentProfile | null> {
    const agentString = await this.redisService.hget(AGENT_HASH_KEY, name);
    if (!agentString) {
      return null; // 根据指令，找不到时返回 null
    }
    return JSON.parse(agentString as string) as AgentProfile;
  }

  async updateAgent(name: string, updateAgentDto: UpdateAgentDto): Promise<AgentProfile | null> {
    const existingAgent = await this.findAgentByName(name);
    if (!existingAgent) {
      throw new NotFoundException(`Agent with name '${name}' not found.`);
    }

    const updatedAgent: AgentProfile = {
      ...existingAgent, // existingAgent
      ...updateAgentDto,
      name, // 确保 name 字段被正确设置或覆盖
      updatedAt: new Date(),
    };

    await this.redisService.hset(AGENT_HASH_KEY, name, JSON.stringify(updatedAgent));
    return updatedAgent;
  }

  async deleteAgent(name: string): Promise<boolean> {
    const result = await this.redisService.hdel(AGENT_HASH_KEY, name);
    if (result > 0) {
      // If deletion from hash was successful, remove from the list as well
      await this.redisService.lrem(AGENT_PROFILE_NAME_LIST_KEY, 0, name); // count = 0 removes all occurrences
    }

    if (result === 0) {
        // 如果希望在尝试删除不存在的 agent 时也抛出 NotFoundException，可以在这里添加
        // throw new NotFoundException(`Agent with name '${name}' not found for deletion.`);
        return false; // 或者根据业务需求返回 false
    }
    return result > 0;
  }
}