import {
  Controller,
  Post,
  Body,
  ValidationPipe,
  Get,
  UseGuards,
} from '@nestjs/common';
import { AgentService } from './agent.service.js';
import { RunAgentDto } from './dto/run-agent.dto.js';

import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';

@Controller('/agent') 
export class AgentController {
  constructor(
    private readonly agentService: AgentService,
  ) {}

  @Post('/run')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async runAgent(@Body(new ValidationPipe({ transform: true })) runAgentDto: RunAgentDto) {
    const { agentName, initialMessage } = runAgentDto;
    return this.agentService.runFullAgentProcess(agentName, { message: initialMessage });
  }

  @Get('/tools')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async getAvailableTools() {
    return this.agentService.getAvailableTools();
  }

  @Get('/models')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async getAvailableModels() {
    return this.agentService.getAvailableModels();
  }

}