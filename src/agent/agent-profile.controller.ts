import {
  Controller,
  Post,
  Body,
  ValidationPipe,
  Get,
  Param,
  Patch,
  Delete,
  HttpCode,
  HttpStatus,
  UseGuards
} from '@nestjs/common';
import { AgentProfileService } from './agent-profile.service.js';
import { CreateAgentDto } from './dto/create-agent.dto.js';
import { UpdateAgentDto } from './dto/update-agent.dto.js';
import { AuthGuard } from '../common/guards/auth.guard.js';
import { AuthPermissions } from '../common/decorators/auth.decorator.js';
import { PermissionLevel } from '../common/decorators/auth.enum.js';

@Controller('/agent-profile')
export class AgentProfileController {
  constructor(private readonly agentProfileService: AgentProfileService) {}

  @Post()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async createAgentProfile(@Body(new ValidationPipe({ transform: true })) createAgentDto: CreateAgentDto) {
    return this.agentProfileService.createAgent(createAgentDto);
  }

  @Get()
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async findAllAgentProfiles() {
    return this.agentProfileService.findAllAgents();
  }

  @Get('/:name')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async findAgentProfileByName(@Param('name') name: string) {
    return this.agentProfileService.findAgentByName(name);
  }

  @Patch('/:name')
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async updateAgentProfile(
    @Param('name') name: string,
    @Body(new ValidationPipe({ transform: true })) updateAgentDto: UpdateAgentDto,
  ) {
    return this.agentProfileService.updateAgent(name, updateAgentDto);
  }

  @Delete('/:name')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(AuthGuard)
  @AuthPermissions(PermissionLevel.READ_WRITE)
  async deleteAgentProfile(@Param('name') name: string): Promise<void> {
    await this.agentProfileService.deleteAgent(name);
  }
}