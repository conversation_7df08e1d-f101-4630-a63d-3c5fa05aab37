// agent/agent.ts
import { OpenAI } from "openai";
import Model from './model.js';
import Tools from './tools.js';
import { Logger } from "@nestjs/common";

export interface AgentOptions {
  name: string;
  model: Model;
  prompt: string;
  temperature?: number;
  maxTokens?: number;
  responseFormat?: object;
  subAgents?: Agent[];
  tools?: Tools[];
  beforeAgentCallback?: (systemPrompt: string) => Promise<string>;
  afterAgentCallback?: (replyContent: string) => Promise<void>;
}

interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string | null;
  name?: string;
  tool_call_id?: string;
  tool_calls?: ToolCall[];
}

interface ToolCallFunction {
  name: string;
  arguments: string; // JSON string
}

interface ToolCall {
  id: string;
  type: 'function';
  function: ToolCallFunction;
}

interface CompletionChoice {
  message: ChatMessage;
  finish_reason?: string;
  // other possible properties
}

export interface CompletionResponse {
  id?: string;
  object?: string;
  created?: number;
  model?: string;
  choices: CompletionChoice[];
  // other possible properties, like usage
}

interface RequestOptions {
  temperature?: number;
  maxTokens?: number;
  responseFormat? :object;
  tools?: Tools[];
}

/**
 * Agent class for interacting with a Model Context Protocol compliant client.
 * Provides a simplified interface for making requests.
 */
class Agent {
  public name: string;
  private client: OpenAI;
  private model: string;
  private temperature: number;
  private maxTokens: number;
  private prompt: string;
  private finalPrompt: string;
  private responseFormat: object;
  public subAgents: Agent[];
  public tools: Tools[];
  private afterAgentCallback?: (replyContent: string) => Promise<void>;
  private beforeAgentCallback?:  (systemPrompt: string) => Promise<string>;
  private logger: Logger;

  constructor(options: AgentOptions) {
    this.name = options.name;
    this.client = options.model.getClient();
    this.model = options.model.getModel();
    this.temperature = options.temperature ?? 0.7;
    this.maxTokens = options.maxTokens ?? 4096;
    this.prompt = options.prompt;
    this.responseFormat = options.responseFormat ?? { type: 'text' }; //json_object
    this.subAgents = options.subAgents ?? [];
    this.tools = options.tools ?? [];
    this.afterAgentCallback = options.afterAgentCallback;
    this.beforeAgentCallback = options.beforeAgentCallback;
    this.logger = new Logger(Agent.name);
  }

  private async systemPrompt(): Promise<string>{
    if(this.beforeAgentCallback && !this.finalPrompt) {
      this.finalPrompt = await this.beforeAgentCallback.call(this, this.prompt)
    }
    else {
      this.finalPrompt = this.prompt
    }

    return this.finalPrompt;
  }

  private async buildContext(message: string): Promise<ChatMessage[]> {
    const context: ChatMessage[] = [];
    context.push({ role: 'system', content: await this.systemPrompt() });
    context.push({ role: 'user', content: message });
    return context;
  }

  private async processToolCalls(
    toolCalls: ToolCall[],
    toolsList: Tools[],
    messages: ChatMessage[],
    options: RequestOptions = {}
  ): Promise<CompletionResponse> {
    const toolResults: ChatMessage[] = [];

    const toolCallPromises = toolCalls.map(async (toolCall): Promise<ChatMessage> => {
      const functionCall = toolCall.function;
      this.logger.debug(`[${this.name}] Tool called: ${functionCall.name}`);
      this.logger.debug(`[${this.name}] Arguments: ${functionCall.arguments}`);

      try {
        const args = JSON.parse(functionCall.arguments);
        const tool = toolsList.find(t => t.hasTool(functionCall.name));

        if (!tool) {
          throw new Error(`Tool not found: ${functionCall.name}`);
        }

        const result = await tool.callTool(functionCall.name, args);
        this.logger.debug(`[${this.name}] Tool result for ${functionCall.name}:`, result);

        let formattedResultContent: string;
        if (typeof result === 'string') {
          formattedResultContent = JSON.stringify({
            content: [{ type: 'text', text: result }]
          });
        } else if (result && result.content && Array.isArray(result.content)) {
          formattedResultContent = JSON.stringify(result);
        } else {
          formattedResultContent = JSON.stringify({
            content: [{ type: 'text', text: JSON.stringify(result) }]
          });
        }

        return {
          tool_call_id: toolCall.id,
          role: 'tool',
          name: functionCall.name,
          content: formattedResultContent
        };
      } catch (error: any) {
        this.logger.error(`Error processing tool call ${functionCall.name}:`, error.stack);
        const errorContent = JSON.stringify({
          content: [{ type: 'text', text: `Error: ${error.message}` }],
          isError: true
        });

        return {
          tool_call_id: toolCall.id,
          role: 'tool',
          name: functionCall.name,
          content: errorContent
        };
      }
    });

    const results = await Promise.all(toolCallPromises);
    toolResults.push(...results);
      
    const updatedMessages: ChatMessage[] = [...messages, ...toolResults];
    const requestPayload = {
      model: this.model,
      messages: updatedMessages,
      temperature: options.temperature !== undefined ? options.temperature : this.temperature,
      max_tokens: options.maxTokens ?? this.maxTokens,
      stream: false,
      tools: (await Promise.all(toolsList.map(tool => tool.listTools()))).flat(),
      response_format: options.responseFormat !== undefined ? options.responseFormat : this.responseFormat
    };
    this.logger.debug(`[${this.name}] OpenAI Request (processToolCalls): ${JSON.stringify(requestPayload)}`);
    const followUpCompletion: CompletionResponse = await (this.client.chat.completions.create as any)(requestPayload);
    this.logger.debug(`[${this.name}] OpenAI Response (processToolCalls): ${JSON.stringify(followUpCompletion)}`);

    const followUpMessage = followUpCompletion.choices[0].message;
    if (followUpMessage.tool_calls && followUpMessage.tool_calls.length > 0) {
      this.logger.debug(`[${this.name}] Follow-up completion has more tool calls, processing recursively...`);
      const nextMessages: ChatMessage[] = [...updatedMessages, followUpMessage];
      return await this.processToolCalls(
        followUpMessage.tool_calls,
        toolsList,
        nextMessages,
        options
      );
    }
    return followUpCompletion;
  }

  public async chatCompletion(message: string, options: RequestOptions = {}): Promise<CompletionResponse> {
    try {
      let currentToolsList: Tools[] = [];
      if (options.tools && Array.isArray(options.tools)) {
        currentToolsList = options.tools;
      } else if (this.tools && Array.isArray(this.tools)) {
        currentToolsList = this.tools;
      }

      let toolsSchema: OpenAI.ChatCompletionTool[] | undefined = undefined;
      if (currentToolsList.length > 0) {
        const promises = currentToolsList.map(tool => tool.listTools());
        const arrayOfArraysOfTools = await Promise.all(promises);
        toolsSchema = arrayOfArraysOfTools.flat();
      }

      const requestPayload = {
        model: this.model,
        messages: await this.buildContext(message),
        temperature: options.temperature !== undefined ? options.temperature : this.temperature,
        max_tokens: options.maxTokens ?? this.maxTokens,
        stream: false,
        tools: toolsSchema,
        response_format: options.responseFormat !== undefined ? options.responseFormat : this.responseFormat
      };
      this.logger.debug(`[${this.name}] OpenAI Request (chatCompletion): ${JSON.stringify(requestPayload)}`);
      const completion: CompletionResponse = await (this.client.chat.completions.create as any)(requestPayload);
      this.logger.debug(`[${this.name}] OpenAI Response (chatCompletion): ${JSON.stringify(completion)}`);

      let assistantMessage = completion.choices[0]?.message;
      let replyContent: string | null = assistantMessage.content;

      if (assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {
        this.logger.debug(`[${this.name}] Initial completion has ${assistantMessage.tool_calls.length} tool calls`);
        const initialMessages: ChatMessage[] = [... await this.buildContext(message), assistantMessage];
        const toolsCompletion = await this.processToolCalls(
          assistantMessage.tool_calls,
          currentToolsList,
          initialMessages,
          options
        );

        replyContent = toolsCompletion.choices[0]?.message?.content;
      }

      if (this.afterAgentCallback) {
        await this.afterAgentCallback.call(this, replyContent);
      }

      if (replyContent && this.subAgents.length > 0) {
        const subAgentPromises = this.subAgents.map(agent =>
          agent.chatCompletion(replyContent as string, options)
        );
        await Promise.all(subAgentPromises);
      }

      return completion;

    } catch (error) {
      this.logger.error(`[${this.name}] API error: ${error.message}`, error.stack);
      throw error;
    }
  }
}

export default Agent;