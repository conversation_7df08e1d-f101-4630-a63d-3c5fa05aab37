import { ClientOptions, OpenAI } from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * Configuration options for the Model class
 */
export interface ModelOptions {
  apiKey?: string;
  baseURL?: string;
  provider: string; // Corresponds to the 'model' parameter for the Model class
  model: string; // Allow 'model' directly in config as well
  [key: string]: any; // Allow other properties
}


/**
 * Agent class for interacting with OpenAI API
 */
class Model {
  private options: ModelOptions
  private client: OpenAI;

  /**
   * Create a new Agent instance
   * @param {ModelOptions} options - Configuration options
   */
  constructor(options: ModelOptions) {
    this.options = options;

    this.client = this._createClient();
  }

  /**
   * Create OpenAI client with current configuration
   * @private
   * @returns {OpenAI} OpenAI client instance
   */
  private _createClient(): OpenAI {
    const config: ClientOptions = this.options;

    const proxy = process.env.PROXY;
    if (proxy) {
      const agent = new HttpsProxyAgent(proxy);
      if (!config.fetchOptions) {
        config.fetchOptions = {};
      }
      (config.fetchOptions as any).dispatcher = agent; // Assert 'fetchOptions' to 'any'
    }

    return new OpenAI(config);
  }

  public getClient(): OpenAI {
    return this.client;
  }

  public getModel(): string {
    return this.options.model;
  }
}

export default Model;