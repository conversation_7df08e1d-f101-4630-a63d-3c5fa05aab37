import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsJSON,
  ValidateNested,
  IsEnum,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TaskLogicEnum } from '../../common/enums/task-logic.enum.js';
import { AgentSchema } from '../parser.js';
import { AgentSchemaSerializable } from '../agent-profile.js';

class HandlerDto {
  @IsEnum(TaskLogicEnum)
  @IsNotEmpty()
  logicId: TaskLogicEnum;

  @IsObject()
  @IsOptional()
  params?: Record<string, any>;
}

export class CreateAgentDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsObject() // Changed from IsJSON as configSchema is now an object
  @IsNotEmpty()
  configSchema: AgentSchemaSerializable;

  @IsObject() // Assuming graphSchema will also be an object
  @IsNotEmpty()
  graphSchema: any;

  @ValidateNested()
  @Type(() => HandlerDto)
  @IsNotEmpty() // Assuming messageProviderHandler is mandatory
  messageProviderHandler: HandlerDto;

  // prevHandler and postHandler removed as per user instruction
}