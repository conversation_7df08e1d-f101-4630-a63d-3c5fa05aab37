import { Modu<PERSON> } from '@nestjs/common';
import { AgentService } from './agent.service.js';
import { AgentProfileService } from './agent-profile.service.js';
import { AgentController } from './agent.controller.js';
import { AgentProfileController } from './agent-profile.controller.js'; // 导入 AgentProfileController
import { RedisModule } from '../redis/redis.module.js';
import { CommonModule } from '../common/common.module.js';
import { AuthModule } from '../auth/auth.module.js';

@Module({
  imports: [RedisModule, CommonModule, AuthModule],
  controllers: [AgentController, AgentProfileController], // 添加 AgentProfileController
  providers: [AgentService, AgentProfileService],
  exports: [AgentService, AgentProfileService], // 添加 AgentProfileService 到 exports
})
export class AgentModule {}