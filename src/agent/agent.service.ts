import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import Parser, { AgentSchema, Callbacks } from './parser.js';
import { RedisService } from '../redis/redis.service.js'; // RedisService 仍然需要用于其他潜在的非 Profile 操作
import { TaskExecutionService } from '../common/services/task-execution.service.js';
import { TaskLogicEnum } from '../common/enums/task-logic.enum.js';
import { AgentProfileService } from './agent-profile.service.js'; // 导入 AgentProfileService
import { TaskLogic } from '../common/decorators/task-logic.decorator.js';
import { TaskParam } from '../common/decorators/task-param.decorator.js';

import Tools, { ToolsConfig, ToolsConfigOptions } from './tools.js'; // 确保路径正确
import { ModelOptions } from './model.js'

import { AgentProfile, AgentSchemaSerializable, TaskLogicHandler } from './agent-profile.js';




@Injectable()
export class AgentService {
  private readonly logger = new Logger(AgentService.name);
  private readonly parser: Parser;

  constructor(
    private readonly redisService: RedisService, // 保留 RedisService，以防将来 AgentService 直接需要它
    private readonly taskExecutionService: TaskExecutionService,
    private readonly agentProfileService: AgentProfileService, // 注入 AgentProfileService
  ) {
    this.parser = new Parser();
  }

  /**
   * Runs an agent based on the provided JSON configuration.
   * @param agentJson The agent configuration in JSON format (conforming to AgentSchema).
   * @param initialMessage The initial message or prompt to start the agent's task.
   * @param callbacks Optional callbacks for agent and tool execution.
   * @returns A promise that resolves to the agent's execution result.
   */
  async runAgentFromJson(
    agentJson: AgentSchema,
    initialMessage: string,
    callbacks?: Callbacks,
  ): Promise<any | null> {
    try {
      const agentInstance: any = this.parser.createAgent(agentJson, callbacks);
      const result = await agentInstance.chatCompletion(initialMessage);
      return result;
    } catch (error) {
      console.error('Error running agent from JSON:', error);
      throw error;
    }
  }

  @TaskLogic(TaskLogicEnum.RUN_AGENT)
  public async runFullAgentProcess(@TaskParam("name") agentName: string, @TaskParam("params") messageProviderParams?: Record<string, any>): Promise<any> {
    this.logger.log(`Starting full agent process for: ${agentName}`);
    try {
      // a. 获取 Agent Profile
      const agentProfile = await this.agentProfileService.findAgentByName(agentName);
      if (!agentProfile) {
        this.logger.error(`Agent profile with name '${agentName}' not found.`);
        throw new NotFoundException(`Agent profile with name '${agentName}' not found.`);
      }
      this.logger.debug(`Successfully fetched AgentProfile for ${agentName}: ${JSON.stringify({ name: agentProfile.name, description: agentProfile.description })}`);

      // b. 准备并校验生效的 Agent Schema (包括从 Redis覆写 model 和 tools 配置)
      //    同时校验 Agent Profile 的顶层处理器配置
      if (!agentProfile.configSchema) {
        this.logger.error(`[${agentName}] Agent profile configSchema is missing or empty.`);
        throw new BadRequestException('Agent profile configSchema is missing or empty.');
      }


      // Fetch global configs once before calling the recursive preparation function
      const modelConfigString = await this.redisService.get('ai_model_config');
      let globalModelConfigs: Record<string, Omit<ModelOptions, 'name' | 'models'>> = {};
      if (modelConfigString) {
        try {
          globalModelConfigs = JSON.parse(modelConfigString);
        } catch (e) {
          this.logger.error(`[${agentName}] Failed to parse global ai_model_config from Redis: ${e.message}`, e.stack);
        }
      }

      const mcpConfigString = await this.redisService.get('ai_mcp_config');
      let globalMcpServersConfig: Record<string, ToolsConfigOptions> = {};
      if (mcpConfigString) {
        try {
          const parsedMcpConfig = JSON.parse(mcpConfigString);
          if (parsedMcpConfig && parsedMcpConfig.mcpServers) {
            globalMcpServersConfig = parsedMcpConfig.mcpServers;
          } else {
            this.logger.warn(`[${agentName}] Global ai_mcp_config from Redis is missing 'mcpServers' property.`);
          }
        } catch (e) {
          this.logger.error(`[${agentName}] Failed to parse global ai_mcp_config from Redis: ${e.message}`, e.stack);
        }
      }

      this._validateAgentProfileTopLevelHandlers(agentProfile, agentName);
      const effectiveSchema = await this._prepareEffectiveAgentSchema(
        agentProfile.configSchema, // This is AgentSchemaSerializable
        agentName, // For top-level logging path/identifier
        globalModelConfigs,
        globalMcpServersConfig
      );
     
      // c. 执行 messageProviderHandler 获取 initialMessage
      this.logger.log(`Executing messageProviderHandler: ${agentProfile.messageProviderHandler.logicId} with params: ${JSON.stringify(agentProfile.messageProviderHandler.params || {})}`);
      const initialMessagePayload = await this.taskExecutionService.executeLogic(
        agentProfile.messageProviderHandler.logicId as TaskLogicEnum,
        {
          ... agentProfile.messageProviderHandler.params,
          ... messageProviderParams
        },
      );
      const initialMessage = typeof initialMessagePayload === 'string' ? initialMessagePayload : JSON.stringify(initialMessagePayload);
      this.logger.debug(`messageProviderHandler ${agentProfile.messageProviderHandler.logicId} completed. Initial message length: ${initialMessage.length}`);
      
      
      // d. 创建回调函数
      const callbacks = this._createCallbacksFromSchema(effectiveSchema); // Removed agentName, it will use schema.name
      this.logger.debug(`[${agentName}] Created callbacks object with keys: ${Object.keys(callbacks).join(', ')}`);


      // e. 执行核心 Agent 逻辑
      this.logger.log(`Executing core agent logic for ${agentName} with initial message and callbacks.`);
      const agentRunResult = await this.runAgentFromJson(
          effectiveSchema,
          initialMessage,
          callbacks,
      );
      this.logger.debug(`Core agent logic for ${agentName} completed. Result type: ${typeof agentRunResult}`);

      this.logger.log(`Full agent process for ${agentName} completed successfully. Returning agent run result.`);
      return agentRunResult;
    } catch (error) {
      this.logger.error(`Error during full agent process for ${agentName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private _validateAgentProfileTopLevelHandlers(agentProfile: AgentProfile, agentName: string): void {
    this.logger.debug(`[${agentName}] Validating top-level handlers for AgentProfile.`);

    // Validate messageProviderHandler (Mandatory)
    if (!agentProfile.messageProviderHandler) {
      const errorMsg = `[${agentName}] Agent profile messageProviderHandler is missing.`;
      this.logger.error(errorMsg);
      throw new BadRequestException(errorMsg);
    }
    if (!agentProfile.messageProviderHandler.logicId) {
      const errorMsg = `[${agentName}] messageProviderHandler.logicId is missing.`;
      this.logger.error(errorMsg);
      throw new BadRequestException(errorMsg);
    }
    if (!this.taskExecutionService.isLogicRegistered(agentProfile.messageProviderHandler.logicId as TaskLogicEnum)) {
      const errorMsg = `[${agentName}] messageProviderHandler.logicId '${agentProfile.messageProviderHandler.logicId}' is not a registered task logic.`;
      this.logger.error(errorMsg);
      throw new BadRequestException(errorMsg);
    }
    // prevHandler and postHandler validation removed as per user instruction
    this.logger.debug(`[${agentName}] Top-level handlers validated successfully.`);
  }


  private async _prepareEffectiveAgentSchema(
    profileConfigSchema: AgentSchemaSerializable, // Changed type here
    loggingPath: string,
    globalModelConfigs: Record<string, Omit<ModelOptions, 'name' | 'models'>>,
    globalMcpServersConfig: Record<string, ToolsConfigOptions>
  ): Promise<AgentSchemaSerializable> { // Return type also changed
    this.logger.debug(`[${loggingPath}] Preparing and validating effective schema (AgentSchemaSerializable).`);
    // Deep clone to avoid mutating the original profileSchema or parent schemas in recursion
    const effectiveSchema: AgentSchemaSerializable = JSON.parse(JSON.stringify(profileConfigSchema));

    // 1. Validate and Override ModelOptions
    if (effectiveSchema.model) {
      if (!effectiveSchema.model.provider || !effectiveSchema.model.model) {
        const errorMsg = `[${loggingPath}] Model configuration is incomplete. 'provider' and 'model' fields are required. Found: ${JSON.stringify(effectiveSchema.model)}`;
        this.logger.error(errorMsg);
        throw new BadRequestException(errorMsg);
      }
      const providerName = effectiveSchema.model.provider;
      if (globalModelConfigs[providerName]) {
        const redisProviderConfig = globalModelConfigs[providerName];
        this.logger.debug(`[${loggingPath}] Overriding model config for provider '${providerName}' with Redis data.`);
        effectiveSchema.model = {
          ...redisProviderConfig,
          provider: effectiveSchema.model.provider,
          model: effectiveSchema.model.model,
        };
      } else {
        this.logger.warn(`[${loggingPath}] No Redis config found for model provider: '${providerName}'. Using schema's model config. Ensure it's complete (e.g. apiKey, baseUrl if not OpenAI).`);
      }
    } else {
      const errorMsg = `[${loggingPath}] Model configuration (effectiveSchema.model) is missing.`;
      this.logger.error(errorMsg);
      throw new BadRequestException(errorMsg);
    }


    // 2. Validate and Override ToolsConfigOptions
    if (effectiveSchema.tools && Array.isArray(effectiveSchema.tools)) {
      effectiveSchema.tools = effectiveSchema.tools.map(toolConfig => {
        if (!toolConfig.name) {
          const errorMsg = `[${loggingPath}] Tool configuration is incomplete. 'name' field is required for each tool. Found: ${JSON.stringify(toolConfig)}`;
          this.logger.error(errorMsg);
          throw new BadRequestException(errorMsg);
        }
        if (globalMcpServersConfig[toolConfig.name]) {
          const redisServerConfigOptions = globalMcpServersConfig[toolConfig.name];
          this.logger.debug(`[${loggingPath}] Overriding tool options for MCP server '${toolConfig.name}' with Redis data.`);
          return {
            ...toolConfig,
            options: { ...redisServerConfigOptions }
          };
        } else {
          this.logger.warn(`[${loggingPath}] No Redis config found for MCP server: '${toolConfig.name}'. Using schema's tool config options.`);
        }
        return toolConfig;
      });
    }

    // 3. Validate Callbacks (afterAgentCallbackHandler, afterToolCallbackHandler from AgentSchemaSerializable)
    // These are distinct from the AgentProfile's top-level prev/post/message handlers
    if (effectiveSchema.afterAgentCallbackHandler) {
        const handler = effectiveSchema.afterAgentCallbackHandler;
        if (handler.logicId && !this.taskExecutionService.isLogicRegistered(handler.logicId as TaskLogicEnum)) {
          const errorMsg = `[${loggingPath}] afterAgentCallbackHandler.logicId '${handler.logicId}' is not a registered task logic.`;
          this.logger.error(errorMsg);
          throw new BadRequestException(errorMsg);
        }
    }
    if (effectiveSchema.beforeAgentCallbackHandler) {
        const handler = effectiveSchema.beforeAgentCallbackHandler;
        if (handler.logicId && !this.taskExecutionService.isLogicRegistered(handler.logicId as TaskLogicEnum)) {
          const errorMsg = `[${loggingPath}] beforeAgentCallbackHandler.logicId '${handler.logicId}' is not a registered task logic.`;
          this.logger.error(errorMsg);
          throw new BadRequestException(errorMsg);
        }
    }
    // Note: The original `callbacks` property on AgentSchema (with direct functions) is not used by AgentProfile.
    // AgentSchemaSerializable introduces `afterToolCallbackHandler` and `afterAgentCallbackHandler` which are TaskLogicHandler.

    // 4. Recursively process and validate subAgents
    if (effectiveSchema.subAgents && Array.isArray(effectiveSchema.subAgents)) {
      this.logger.debug(`[${loggingPath}] Processing ${effectiveSchema.subAgents.length} sub-agent(s).`);
      effectiveSchema.subAgents = await Promise.all(
        effectiveSchema.subAgents.map(async (subAgentSchema, index) => {
          const subAgentLoggingPath = `${loggingPath}/subAgent[${index}]${subAgentSchema.name ? `(${subAgentSchema.name})` : ''}`;
          // subAgentSchema is AgentSchema, but _prepareEffectiveAgentSchema expects AgentSchemaSerializable.
          // We need to cast it or ensure it conforms. Since subAgents are defined as AgentSchema[],
          // they won't have afterToolCallbackHander/afterAgentCallbackHander unless explicitly added.
          // For now, we assume they are compatible or the recursive call handles it.
          // If subAgentSchema is strictly AgentSchema, it will pass through the callback checks above safely.
          return this._prepareEffectiveAgentSchema(
            subAgentSchema as AgentSchemaSerializable, // Cast needed as subAgents are AgentSchema[]
            subAgentLoggingPath,
            globalModelConfigs,
            globalMcpServersConfig
          );
        })
      );
    }
    
    return effectiveSchema;
  }

  /**
   * Parses JSON content from a string that might be wrapped in Markdown code blocks (```json ... ```).
   *
   * @param markdownString The string potentially containing Markdown-wrapped JSON.
   * @returns The parsed JavaScript object/array if successful, otherwise null.
   */
  private parseMarkdownJson(markdownString: string | null): any | null {
    if (markdownString == null || typeof markdownString !== 'string' || markdownString.length === 0) {
      return { payload: markdownString };
    }

    const regex = /```json\s*([\s\S]*?)\s*```/;
    const match = markdownString.match(regex);

    let jsonContent = markdownString;
    if (match && match[1]) {
      jsonContent = match[1];
    }

    jsonContent = jsonContent.trim()

    if (!jsonContent) {
        return {
          payload: markdownString
        };
    }

    try {
      return {
        ... JSON.parse(jsonContent),
        payload: jsonContent
      };
    } catch (error) {
      console.error("Failed to parse JSON content from Markdown:", error);
      // You might want to log the problematic jsonContent for debugging:
      // console.error("Problematic JSON string:", jsonContent);
      return {
        payload: markdownString
      };
    }

  }


  private async _executeAfterAgentCallback(handler: TaskLogicHandler, replyContent: string | null, agentName: string): Promise<void> {
    this.logger.log(`[${agentName}] Executing afterAgentCallback via handler: ${handler.logicId}`);

    try {

      let replyParams = this.parseMarkdownJson(replyContent)
     
      await this.taskExecutionService.executeLogic(
        handler.logicId as TaskLogicEnum,
        { ...handler.params, ...replyParams , agentName } // Pass replyContent and agentName as params
      );
      this.logger.debug(`[${agentName}] afterAgentCallback handler ${handler.logicId} executed successfully.`);
    } catch (error) {
      this.logger.error(`[${agentName}] Error executing afterAgentCallback handler ${handler.logicId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async _executeBeforeAgentCallback(handler: TaskLogicHandler, systemPrompt: string, agentName: string): Promise<string> {
    this.logger.log(`[${agentName}] Executing beforeAgentCallback handler via handler: ${handler.logicId}`);
    try {
      systemPrompt = await this.taskExecutionService.executeLogic(
        handler.logicId as TaskLogicEnum,
        { ...handler.params, systemPrompt, agentName } // Pass tool details and agentName as params
      );
      this.logger.debug(`[${agentName}] beforeAgentCallback handler ${handler.logicId}  executed successfully.`);
      return systemPrompt;
    } catch (error) {
      this.logger.error(`[${agentName}] Error executing beforeAgentCallback handler ${handler.logicId} for error: ${error.message}`, error.stack);
      throw error;
    }
  }

  private _createCallbacksFromSchema(schema: AgentSchemaSerializable): Callbacks {
    const callbacks: Callbacks = {};

    const processSchema = (currentSchema: AgentSchemaSerializable, path: string) => {
      const agentName = currentSchema.name;
      
      const agentSpecificCallbacks: {
        afterAgentCallback?: (replyContent: string) => Promise<void>;
        beforeAgentCallback?: (systemPrompt: string) => Promise<string>;
      } = {};

      if (currentSchema.afterAgentCallbackHandler && currentSchema.afterAgentCallbackHandler.logicId) {
        const handler = currentSchema.afterAgentCallbackHandler;
        agentSpecificCallbacks.afterAgentCallback = (replyContent: string | null): Promise<void> => {
           return this._executeAfterAgentCallback(handler, replyContent, agentName);
        };
      }

      if (currentSchema.beforeAgentCallbackHandler && currentSchema.beforeAgentCallbackHandler.logicId) {
        const handler = currentSchema.beforeAgentCallbackHandler;
        agentSpecificCallbacks.beforeAgentCallback = (systemPrompt: string): Promise<string> => {
           return this._executeBeforeAgentCallback(handler, systemPrompt, agentName);
        };
      }

      if (Object.keys(agentSpecificCallbacks).length > 0) {
        callbacks[agentName] = agentSpecificCallbacks;
      }

      if (currentSchema.subAgents && Array.isArray(currentSchema.subAgents)) {
        currentSchema.subAgents.forEach((subAgent, index) => {
          // Ensure subAgent is treated as AgentSchemaSerializable.
          // If subAgents are strictly AgentSchema, they won't have the Handlers.
          // This assumes subAgents can also have these serializable handlers.
          processSchema(subAgent as AgentSchemaSerializable, `${path}/subAgent[${index}]`);
        });
      }
    };

    processSchema(schema, schema.name);
    this.logger.debug(`[${schema.name}] Generated callbacks structure with keys: ${Object.keys(callbacks).join(', ')}`);
    return callbacks;
  }

  async getAvailableTools(): Promise<ToolsConfig[]> {
    this.logger.log('Fetching available MCP tools from Redis config');
    const toolsConfigList: ToolsConfig[] = [];

    const mcpConfigString = await this.redisService.get('ai_mcp_config');
    if (!mcpConfigString) {
      this.logger.warn('MCP config string not found in Redis (key: ai_mcp_config). Returning empty list.');
      return [];
    }

    const mcpConfig = JSON.parse(mcpConfigString);
    if (!mcpConfig || !mcpConfig.mcpServers) {
      this.logger.warn('MCP config JSON is invalid or mcpServers not found. Returning empty list.');
      return [];
    }

    const mcpServers: Record<string, any> = mcpConfig.mcpServers;

    for (const serverName in mcpServers) {
        const serverConfig = mcpServers[serverName];
        const toolsConfigOptions :ToolsConfigOptions = {
          ... serverConfig
        };

        const toolsConfig :ToolsConfig = {
          name: serverName,
          transport: serverConfig.command ? 'stdio' : 'sse',
          options: toolsConfigOptions
        };

        toolsConfigList.push(toolsConfig);
    }

    this.logger.log(`Successfully fetched ${toolsConfigList.length} MCP tools.`);
    return toolsConfigList;
  }

  async getAvailableModels(): Promise<ModelOptions[]> {
    this.logger.log('Fetching available AI models from Redis config');
    const models: ModelOptions[] = [];

    try {
      const modelConfigString = await this.redisService.get('ai_model_config');
      if (!modelConfigString) {
        this.logger.warn('AI model config string not found in Redis (key: ai_model_config). Returning empty list.');
        return [];
      }

      const modelConfig = JSON.parse(modelConfigString);
      if (!modelConfig) {
        this.logger.warn('AI model config JSON is invalid. Returning empty list.');
        return [];
      }

      for (const provider in modelConfig) {
        const providerConfig = modelConfig[provider];
        if (providerConfig && Array.isArray(providerConfig.models)) {
          providerConfig.models.forEach((model: string) => {
            models.push({
              provider: provider,
              model: model,
            });
          });
        } else {
          this.logger.warn(`Invalid or missing models array for provider: ${provider}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error fetching or processing AI models config: ${error.message}`, error.stack);
      return []; // Return empty on error
    }
    
    this.logger.log(`Successfully fetched ${models.length} AI models.`);
    return models;
  }


  @TaskLogic(TaskLogicEnum.RENEW_CURRENT_DATE)
  async renewAgentTime(@TaskParam("systemPrompt") systemPrompt: string): Promise<string> {

    this.logger.log(`Renewing currentDate in systemPrompt. Original prompt received: "${systemPrompt.substring(0, 100)}..."`);
    
    // 获取上海时区的当前日期并格式化为 YYYY-MM-DD
    // 根据环境信息，当前日期是 2025-06-03
    // 对于动态获取和格式化：
    const now = new Date();
    const shanghaiTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));

    const year = shanghaiTime.getFullYear();
    const month = String(shanghaiTime.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始
    const day = String(shanghaiTime.getDate()).padStart(2, '0');

    const formattedDate = `${year}-${month}-${day}`;

    const updatedSystemPrompt = systemPrompt.replace(/\$\{currentDate\}/g, formattedDate);
    
    if (systemPrompt !== updatedSystemPrompt) {
      this.logger.log(`Successfully replaced \${currentDate} with ${formattedDate}.`);
    } else {
      this.logger.warn(`Placeholder \${currentDate} not found in the systemPrompt.`);
    }
    
    return updatedSystemPrompt;
  }


  @TaskLogic(TaskLogicEnum.MESSAGE_PROVIDER)
  async messageProvider(@TaskParam("message") message: string): Promise<string> {

    this.logger.log(`message provider in params : "${message.substring(0, 100)}..."`);
  
    return message;
  }
}