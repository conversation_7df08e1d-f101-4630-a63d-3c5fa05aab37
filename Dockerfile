# Stage 1: Build the application
FROM node:lts-alpine AS builder

WORKDIR /usr/src/app

# Install pnpm
RUN npm install -g pnpm

# Copy package.json and pnpm-lock.yaml
# Ensure pnpm-lock.yaml is present in your project directory when building
COPY package.json pnpm-lock.yaml ./

# Install dependencies using pnpm
# --frozen-lockfile ensures pnpm-lock.yaml is not modified and is strictly followed
RUN pnpm install --frozen-lockfile

# Copy the rest of the application source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production environment
FROM lwthiker/curl-impersonate:0.6-chrome AS production

# Alpine images often run as root by default.
# USER root # This might not be needed if the base image is already root.

ENV NODE_ENV=production

WORKDIR /usr/src/app

# Install Node.js and npm using apk for Alpine-based image
# curl should be available in lwthiker/curl-impersonate images.
# If 'npm' is not bundled with 'nodejs' package on this specific Alpine, it's added here.
RUN apk add --no-cache nodejs npm tzdata

# Set timezone to Asia/Shanghai
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install pnpm
RUN npm install -g pnpm

# Copy package.json and pnpm-lock.yaml
# Ensure pnpm-lock.yaml is present for installing production dependencies consistently
COPY package.json pnpm-lock.yaml ./

# Install only production dependencies using pnpm
RUN pnpm install --prod --frozen-lockfile

# Copy the built application from the builder stage
COPY --from=builder /usr/src/app/dist ./dist

# Expose the application port (default NestJS port is 3000)
EXPOSE 3000

# Command to run the application
# Based on "start:prod": "node dist/main" from package.json
CMD ["node", "dist/main.js"]
