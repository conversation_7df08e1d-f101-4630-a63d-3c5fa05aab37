{"name": "central-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@golevelup/nestjs-discovery": "^5.0.0", "@liaoliaots/nestjs-redis": "^10.0.0", "@modelcontextprotocol/sdk": "^1.11.4", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@octokit/auth-app": "^7.2.1", "@octokit/rest": "^21.1.1", "@octokit/types": "^14.0.0", "@octokit/webhooks": "^14.0.0", "@types/cheerio": "^1.0.0", "@types/js-yaml": "^4.0.9", "axios": "^1.9.0", "base64-image-mime": "^1.0.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie": "^1.0.2", "cookie-parser": "^1.4.7", "cron": "^4.3.0", "cron-parser": "^5.2.0", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "ioredis": "^5.6.1", "js-yaml": "^4.1.0", "openai": "^5.0.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/cookie-parser": "^1.4.8", "@types/cron": "^2.4.3", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}